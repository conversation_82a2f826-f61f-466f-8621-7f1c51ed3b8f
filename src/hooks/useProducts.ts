import { useState, useMemo } from 'react';
import { Product, ProductFilters, ProductSortOption } from '@/types/product';
import { allProducts } from '@/data/products';

export const sortOptions: ProductSortOption[] = [
  { value: 'featured', label: 'Featured' },
  { value: 'newest', label: 'Newest First' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'name', label: 'Name A-Z' },
  { value: 'rating', label: 'Highest Rated' }
];

export const useProducts = (initialFilters?: ProductFilters) => {
  const [filters, setFilters] = useState<ProductFilters>(initialFilters || {});
  const [sortBy, setSortBy] = useState<string>('featured');

  const filteredProducts = useMemo(() => {
    let filtered = [...allProducts];

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(product => 
        product.category.toLowerCase() === filters.category?.toLowerCase()
      );
    }

    // Apply subcategory filter
    if (filters.subcategory) {
      filtered = filtered.filter(product => 
        product.subcategory?.toLowerCase() === filters.subcategory?.toLowerCase()
      );
    }

    // Apply price range filter
    if (filters.priceRange) {
      filtered = filtered.filter(product => 
        product.price >= (filters.priceRange?.min || 0) &&
        product.price <= (filters.priceRange?.max || Infinity)
      );
    }

    // Apply tags filter
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(product =>
        filters.tags?.some(tag => 
          product.tags.some(productTag => 
            productTag.toLowerCase().includes(tag.toLowerCase())
          )
        )
      );
    }

    // Apply stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => product.stock > 0);
    }

    // Apply new items filter
    if (filters.isNew) {
      filtered = filtered.filter(product => product.isNew);
    }

    // Apply sale items filter
    if (filters.isOnSale) {
      filtered = filtered.filter(product => product.isOnSale);
    }

    return filtered;
  }, [filters]);

  const sortedProducts = useMemo(() => {
    const sorted = [...filteredProducts];

    switch (sortBy) {
      case 'featured':
        return sorted.sort((a, b) => {
          if (a.isFeatured && !b.isFeatured) return -1;
          if (!a.isFeatured && b.isFeatured) return 1;
          return 0;
        });
      
      case 'newest':
        return sorted.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      
      case 'price-low':
        return sorted.sort((a, b) => a.price - b.price);
      
      case 'price-high':
        return sorted.sort((a, b) => b.price - a.price);
      
      case 'name':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      
      case 'rating':
        return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      
      default:
        return sorted;
    }
  }, [filteredProducts, sortBy]);

  const updateFilters = (newFilters: Partial<ProductFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getProductById = (id: string): Product | undefined => {
    return allProducts.find(product => product.id === id);
  };

  const getProductsByCategory = (category: string): Product[] => {
    return allProducts.filter(product => 
      product.category.toLowerCase() === category.toLowerCase()
    );
  };

  const getProductsBySubcategory = (subcategory: string): Product[] => {
    return allProducts.filter(product => 
      product.subcategory?.toLowerCase() === subcategory.toLowerCase()
    );
  };

  const getFeaturedProducts = (): Product[] => {
    return allProducts.filter(product => product.isFeatured);
  };

  const getNewProducts = (): Product[] => {
    return allProducts.filter(product => product.isNew);
  };

  const getSaleProducts = (): Product[] => {
    return allProducts.filter(product => product.isOnSale);
  };

  const getAvailableCategories = (): string[] => {
    const categories = new Set(allProducts.map(product => product.category));
    return Array.from(categories).sort();
  };

  const getAvailableSubcategories = (category?: string): string[] => {
    let products = allProducts;
    if (category) {
      products = products.filter(product => 
        product.category.toLowerCase() === category.toLowerCase()
      );
    }
    
    const subcategories = new Set(
      products
        .map(product => product.subcategory)
        .filter(Boolean) as string[]
    );
    return Array.from(subcategories).sort();
  };

  const getPriceRange = (): { min: number; max: number } => {
    const prices = allProducts.map(product => product.price);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
  };

  return {
    products: sortedProducts,
    filters,
    sortBy,
    updateFilters,
    clearFilters,
    setSortBy,
    getProductById,
    getProductsByCategory,
    getProductsBySubcategory,
    getFeaturedProducts,
    getNewProducts,
    getSaleProducts,
    getAvailableCategories,
    getAvailableSubcategories,
    getPriceRange,
    totalProducts: sortedProducts.length,
    allProducts
  };
};
