export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary?: boolean;
}

export interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  stock?: number;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  images: ProductImage[];
  category: string;
  subcategory?: string;
  tags: string[];
  stock: number;
  variants?: {
    colors?: ProductVariant[];
    sizes?: ProductVariant[];
    materials?: ProductVariant[];
  };
  features?: string[];
  specifications?: Record<string, string>;
  isNew?: boolean;
  isFeatured?: boolean;
  isOnSale?: boolean;
  rating?: number;
  reviewCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CartItem {
  productId: string;
  product: Product;
  quantity: number;
  selectedVariants?: {
    color?: ProductVariant;
    size?: ProductVariant;
    material?: ProductVariant;
  };
}

export interface ProductFilters {
  category?: string;
  subcategory?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  tags?: string[];
  inStock?: boolean;
  isNew?: boolean;
  isOnSale?: boolean;
}

export interface ProductSortOption {
  value: string;
  label: string;
}
