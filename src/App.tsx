import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import GiftsForHer from "./pages/GiftsForHer";
import GiftsForHim from "./pages/GiftsForHim";
import GiftsForHome from "./pages/GiftsForHome";
import GiftsForBabies from "./pages/GiftsForBabies";
import Personalization from "./pages/Personalization";
import ProductDetail from "./pages/ProductDetail";
import Women from "./pages/Women";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/gifts-for-her" element={<GiftsForHer />} />
          <Route path="/gifts-for-him" element={<GiftsForHim />} />
          <Route path="/gifts-for-home" element={<GiftsForHome />} />
          <Route path="/gifts-for-babies" element={<GiftsForBabies />} />
          <Route path="/personalization" element={<Personalization />} />
          <Route path="/women" element={<Women />} />
          <Route path="/product/:id" element={<ProductDetail />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
