import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { CartProvider } from "@/context/CartContext";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Gifts from "./pages/Gifts";
import New from "./pages/New";
import BagsAndWallets from "./pages/BagsAndWallets";
import Women from "./pages/Women";
import Men from "./pages/Men";
import Jewelry from "./pages/Jewelry";
import Watches from "./pages/Watches";
import Perfumes from "./pages/Perfumes";
import TravelAndHome from "./pages/TravelAndHome";
import Services from "./pages/Services";
import About from "./pages/About";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <CartProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/products" element={<Products />} />
            <Route path="/products/:id" element={<ProductDetail />} />
            <Route path="/gifts" element={<Gifts />} />
            <Route path="/new" element={<New />} />
            <Route path="/bags-and-wallets" element={<BagsAndWallets />} />
            <Route path="/women" element={<Women />} />
            <Route path="/men" element={<Men />} />
            <Route path="/jewelry" element={<Jewelry />} />
            <Route path="/watches" element={<Watches />} />
            <Route path="/perfumes" element={<Perfumes />} />
            <Route path="/travel-and-home" element={<TravelAndHome />} />
            <Route path="/services" element={<Services />} />
            <Route path="/about" element={<About />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </CartProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
