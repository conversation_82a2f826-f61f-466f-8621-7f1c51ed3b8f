import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Men: React.FC = () => {
  const menCategories = [
    {
      title: "Bags",
      description: "Sophisticated briefcases, messenger bags, and travel companions",
      href: "/products?category=Men&subcategory=Bags"
    },
    {
      title: "Small Leather Goods",
      description: "Wallets, cardholders, and leather accessories for the modern gentleman",
      href: "/products?category=Men&subcategory=Wallets"
    },
    {
      title: "Shoes",
      description: "Premium footwear from casual sneakers to formal dress shoes",
      href: "/products?category=Men&subcategory=Shoes"
    },
    {
      title: "Accessories",
      description: "Belts, ties, cufflinks, and other refined accessories",
      href: "/products?category=Men&subcategory=Accessories"
    },
    {
      title: "Ready-to-Wear",
      description: "Contemporary menswear that combines comfort with sophistication",
      href: "/products?category=Men&subcategory=Ready-to-Wear"
    },
    {
      title: "Fashion Jewelry",
      description: "Elegant watches, rings, and accessories for the discerning man",
      href: "/jewelry"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  For Him
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Men's
                  <br />
                  Collection
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our men's collection featuring luxury bags, leather goods, shoes, and accessories designed for the contemporary gentleman who values quality and style.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Men" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Men's
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Modern Sophistication
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menCategories.map((category) => (
              <Link
                key={category.title}
                to={category.href}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Collections */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white mb-4">
              Essential Pieces
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { title: "Business Bags", subtitle: "Professional", imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=800&auto=format&fit=crop" },
              { title: "Luxury Watches", subtitle: "Timepieces", imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop" },
              { title: "Dress Shoes", subtitle: "Footwear", imageUrl: "https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=800&auto=format&fit=crop" },
              { title: "Fragrances", subtitle: "Scents", imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop" }
            ].map((item) => (
              <Link
                key={item.title}
                to={`/products?category=Men`}
                className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg"
              >
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-between p-4 text-white">
                  <div className="text-right">
                    <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                      MEN
                    </span>
                  </div>
                  <div className="text-center">
                    <h3 className="font-serif text-lg font-light mb-1">{item.title}</h3>
                    <p className="text-xs text-white/80 mb-2">{item.subtitle}</p>
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Shop Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Men;
