import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const Men: React.FC = () => {
  const menCategories = [
    {
      title: "Bags",
      description: "Sophisticated briefcases, messenger bags, and travel companions",
      imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Small Leather Goods",
      description: "Wallets, cardholders, and leather accessories for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Shoes",
      description: "Premium footwear from casual sneakers to formal dress shoes",
      imageUrl: "https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Accessories",
      description: "Belts, ties, cufflinks, and other refined accessories",
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Ready-to-Wear",
      description: "Contemporary menswear that combines comfort with sophistication",
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Fashion Jewelry",
      description: "Elegant watches, rings, and accessories for the discerning man",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="Men"
      description="Discover our men's collection featuring luxury bags, leather goods, shoes, and accessories designed for the contemporary gentleman who values quality and style."
      backgroundImage="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=2000&auto=format&fit=crop"
      categories={menCategories}
      layout="image-grid"
    />
  );
};

export default Men;
