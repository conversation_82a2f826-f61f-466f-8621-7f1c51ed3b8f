import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const New: React.FC = () => {
  const newCategories = [
    {
      title: "New Arrivals - Women",
      description: "The latest additions to our women's collection",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "New Arrivals - Men",
      description: "Fresh styles and contemporary designs for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Pre-Fall 2025 Collection",
      description: "Transitional pieces that bridge seasons with timeless elegance",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Spring 2025 Collection",
      description: "Light, airy designs that capture the essence of spring renewal",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "New Formal Collection",
      description: "Sophisticated pieces for special occasions and professional settings",
      imageUrl: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Spring-Summer 2025 Show",
      description: "Runway pieces from our latest fashion show",
      imageUrl: "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="New Arrivals"
      description="Be the first to discover our latest creations. From seasonal collections to timeless classics, explore what's new at LORIYA."
      backgroundImage="https://images.unsplash.com/photo-1558769132-cb1aea458c5e?q=80&w=2000&auto=format&fit=crop"
      categories={newCategories}
      layout="image-grid"
    />
  );
};

export default New;
