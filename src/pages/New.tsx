import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const New: React.FC = () => {
  const newCategories = [
    {
      title: "New Arrivals - Women",
      description: "The latest additions to our women's collection",
      href: "/products?category=Women&isNew=true"
    },
    {
      title: "New Arrivals - Men",
      description: "Fresh styles and contemporary designs for the modern gentleman",
      href: "/products?category=Men&isNew=true"
    },
    {
      title: "Pre-Fall 2025 Collection",
      description: "Transitional pieces that bridge seasons with timeless elegance",
      href: "/products?isNew=true"
    },
    {
      title: "Spring 2025 Collection",
      description: "Light, airy designs that capture the essence of spring renewal",
      href: "/products?isNew=true"
    },
    {
      title: "New Formal Collection",
      description: "Sophisticated pieces for special occasions and professional settings",
      href: "/products?isNew=true"
    },
    {
      title: "Spring-Summer 2025 Show",
      description: "Runway pieces from our latest fashion show",
      href: "/products?isNew=true"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1558769132-cb1aea458c5e?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Latest Collections
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  New
                  <br />
                  Arrivals
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Be the first to discover our latest creations. From seasonal collections to timeless classics, explore what's new at LORIYA.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?isNew=true" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop New
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* New Collections */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Fresh Arrivals
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Latest Collections
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {newCategories.map((category) => (
              <Link
                key={category.title}
                to={category.href}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured New Items */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white mb-4">
              This Season's Highlights
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { title: "Handbags", category: "Bags and Wallets", imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop" },
              { title: "Jewelry", category: "Jewelry", imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop" },
              { title: "Perfumes", category: "Perfumes", imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop" },
              { title: "Watches", category: "Watches", imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop" }
            ].map((item) => (
              <Link
                key={item.title}
                to={`/products?category=${encodeURIComponent(item.category)}&isNew=true`}
                className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg"
              >
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-between p-4 text-white">
                  <div className="text-right">
                    <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                      NEW
                    </span>
                  </div>
                  <div className="text-center">
                    <h3 className="font-serif text-xl font-light mb-2">{item.title}</h3>
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Shop New
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default New;
