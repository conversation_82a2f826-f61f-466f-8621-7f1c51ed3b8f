import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, User, ShoppingBag, ArrowLeft, Filter, Grid3X3, List } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { AuthSheet } from "@/components/AuthDialog";

const GiftsForBabies = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');

  const products = [
    {
      id: 1,
      name: "Organic Cotton Blanket",
      price: "$85",
      originalPrice: "$120",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Textiles",
      isNew: true,
      isSale: true
    },
    {
      id: 2,
      name: "Wooden Toy Set",
      price: "$65",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Toys",
      isNew: false,
      isSale: false
    },
    {
      id: 3,
      name: "Silver Baby Rattle",
      price: "$150",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Keepsakes",
      isNew: true,
      isSale: false
    },
    {
      id: 4,
      name: "Cashmere Baby Outfit",
      price: "$180",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Clothing",
      isNew: false,
      isSale: false
    },
    {
      id: 5,
      name: "Personalized Photo Album",
      price: "$95",
      originalPrice: "$130",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Keepsakes",
      isNew: false,
      isSale: true
    },
    {
      id: 6,
      name: "Luxury Baby Carrier",
      price: "$320",
      imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop",
      category: "Accessories",
      isNew: true,
      isSale: false
    }
  ];

  const backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721";

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start items-center space-x-4">
              <Link to="/" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors">
                <ArrowLeft className="w-4 h-4" />
                <span className="text-sm font-light tracking-wide">BACK</span>
              </Link>
            </div>
            <div className="text-center font-serif text-2xl font-light tracking-wider text-white">
              LORIYA
            </div>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Wishlist</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your wishlist is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <AuthSheet>
                <button className="hover:text-gray-200 transition-colors">
                  <User size={20} />
                </button>
              </AuthSheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                      0
                    </span>
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your cart is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Page Header */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <p className="text-sm font-light tracking-widest text-white/70 uppercase mb-4">
                Gift Collection
              </p>
              <h1 className="font-serif text-4xl md:text-6xl font-light text-white mb-6">
                Gifts for Babies
              </h1>
              <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                Celebrate new arrivals with our precious collection of baby gifts, from soft organic textiles 
                to timeless keepsakes that will be treasured for years to come.
              </p>
            </div>
          </div>
        </section>

        {/* Filters and Controls */}
        <section className="px-8 pb-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
              <div className="flex items-center gap-4">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                    <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white mb-8">Filters</h2>
                      </div>
                      <div className="flex-grow space-y-6">
                        <div>
                          <h3 className="text-lg font-light text-white mb-4">Category</h3>
                          <div className="space-y-2">
                            {['All', 'Clothing', 'Toys', 'Textiles', 'Keepsakes', 'Accessories'].map((category) => (
                              <label key={category} className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{category}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-light text-white mb-4">Age Range</h3>
                          <div className="space-y-2">
                            {['0-6 months', '6-12 months', '1-2 years', '2+ years'].map((range) => (
                              <label key={range} className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{range}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
                <p className="text-white/80 font-light">{products.length} items</p>
              </div>
              
              <div className="flex items-center gap-4">
                <select 
                  value={sortBy} 
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white/10 border border-white/30 text-white backdrop-blur-sm rounded px-3 py-2 font-light"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="newest">Newest</option>
                </select>
                
                <div className="flex items-center gap-2">
                  <button 
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white/20' : 'bg-white/10'} text-white hover:bg-white/20 transition-colors`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded ${viewMode === 'list' ? 'bg-white/20' : 'bg-white/10'} text-white hover:bg-white/20 transition-colors`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Products Grid */}
        <section className="px-8 pb-16">
          <div className="max-w-6xl mx-auto">
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6'}>
              {products.map((product) => (
                <div key={product.id} className={viewMode === 'grid' ? 'group' : 'flex gap-6 bg-white/5 backdrop-blur-sm rounded-lg p-6'}>
                  <div className={viewMode === 'grid' ? 'relative aspect-[3/4] overflow-hidden rounded-lg mb-4' : 'relative w-48 h-48 flex-shrink-0 overflow-hidden rounded-lg'}>
                    <img 
                      src={product.imageUrl} 
                      alt={product.name} 
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" 
                    />
                    {product.isNew && (
                      <span className="absolute top-4 left-4 bg-white text-black px-2 py-1 text-xs font-light uppercase tracking-wider">
                        New
                      </span>
                    )}
                    {product.isSale && (
                      <span className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 text-xs font-light uppercase tracking-wider">
                        Sale
                      </span>
                    )}
                    <button className="absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Heart className="w-5 h-5 text-white" />
                    </button>
                  </div>
                  
                  <div className={viewMode === 'grid' ? 'text-center' : 'flex-1 flex flex-col justify-center'}>
                    <p className="text-xs font-light tracking-wider text-white/60 uppercase mb-2">{product.category}</p>
                    <h3 className="text-lg font-light text-white mb-2">{product.name}</h3>
                    <div className="flex items-center gap-2 justify-center">
                      <span className="text-white font-light">{product.price}</span>
                      {product.originalPrice && (
                        <span className="text-white/60 line-through text-sm">{product.originalPrice}</span>
                      )}
                    </div>
                    {viewMode === 'list' && (
                      <Button className="mt-4 bg-white text-black hover:bg-gray-100 font-light tracking-wide w-fit">
                        Add to Cart
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default GiftsForBabies;
