import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Heart, User, ShoppingBag, Filter, Grid3X3, List, Type, Palette } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { AuthSheet } from "@/components/AuthDialog";
import { MainMenu } from "@/components/MainMenu";

const Personalization = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');

  const products = [
    {
      id: 1,
      name: "Monogram Leather Handbag",
      price: "$1,450",
      originalPrice: "$1,650",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop",
      category: "Bags",
      isNew: true,
      isSale: true,
      personalizationOptions: ["Initials", "Color Choice"]
    },
    {
      id: 2,
      name: "Engraved Watch",
      price: "$850",
      imageUrl: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?q=80&w=2099&auto=format&fit=crop",
      category: "Accessories",
      isNew: false,
      isSale: false,
      personalizationOptions: ["Engraving", "Strap Color"]
    },
    {
      id: 3,
      name: "Custom Jewelry Box",
      price: "$320",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2370&auto=format&fit=crop",
      category: "Home",
      isNew: true,
      isSale: false,
      personalizationOptions: ["Initials", "Interior Color"]
    },
    {
      id: 4,
      name: "Personalized Scarf",
      price: "$380",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop",
      category: "Accessories",
      isNew: false,
      isSale: false,
      personalizationOptions: ["Monogram", "Pattern Choice"]
    },
    {
      id: 5,
      name: "Embossed Wallet",
      price: "$450",
      originalPrice: "$520",
      imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop",
      category: "Wallets",
      isNew: false,
      isSale: true,
      personalizationOptions: ["Initials", "Thread Color"]
    },
    {
      id: 6,
      name: "Custom Perfume Bottle",
      price: "$280",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=2008&auto=format&fit=crop",
      category: "Perfumes",
      isNew: true,
      isSale: false,
      personalizationOptions: ["Engraving", "Bottle Color"]
    }
  ];

  const backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721";

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start">
              <MainMenu />
            </div>
            <div className="text-center">
              <Link to="/" className="font-serif text-2xl font-light tracking-wider text-white hover:text-gray-200 transition-colors">
                LORIYA
              </Link>
            </div>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Wishlist</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your wishlist is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <AuthSheet>
                <button className="hover:text-gray-200 transition-colors">
                  <User size={20} />
                </button>
              </AuthSheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                      0
                    </span>
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your cart is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Page Header */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <p className="text-sm font-light tracking-widest text-white/70 uppercase mb-4">
                Bespoke Services
              </p>
              <h1 className="font-serif text-4xl md:text-6xl font-light text-white mb-6">
                Personalization
              </h1>
              <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                Make it uniquely yours. Emboss select bags, luggage, and leather accessories with your initials 
                or choose custom colors to create a truly personal piece.
              </p>
            </div>
            
            {/* Personalization Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                  <Type className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-light text-white mb-2">Monogramming</h3>
                <p className="text-sm font-light text-white/80">Add your initials with elegant embossing or engraving</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                  <Palette className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-light text-white mb-2">Color Customization</h3>
                <p className="text-sm font-light text-white/80">Choose from exclusive color palettes and finishes</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-light text-white mb-2">Bespoke Design</h3>
                <p className="text-sm font-light text-white/80">Create one-of-a-kind pieces with our artisans</p>
              </div>
            </div>
          </div>
        </section>

        {/* Filters and Controls */}
        <section className="px-8 pb-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
              <div className="flex items-center gap-4">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                    <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white mb-8">Filters</h2>
                      </div>
                      <div className="flex-grow space-y-6">
                        <div>
                          <h3 className="text-lg font-light text-white mb-4">Category</h3>
                          <div className="space-y-2">
                            {['All', 'Bags', 'Wallets', 'Accessories', 'Home', 'Perfumes'].map((category) => (
                              <label key={category} className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{category}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-light text-white mb-4">Personalization Type</h3>
                          <div className="space-y-2">
                            {['Monogramming', 'Engraving', 'Color Choice', 'Custom Design'].map((type) => (
                              <label key={type} className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{type}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
                <p className="text-white/80 font-light">{products.length} items</p>
              </div>
              
              <div className="flex items-center gap-4">
                <select 
                  value={sortBy} 
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white/10 border border-white/30 text-white backdrop-blur-sm rounded px-3 py-2 font-light"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="newest">Newest</option>
                </select>
                
                <div className="flex items-center gap-2">
                  <button 
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white/20' : 'bg-white/10'} text-white hover:bg-white/20 transition-colors`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded ${viewMode === 'list' ? 'bg-white/20' : 'bg-white/10'} text-white hover:bg-white/20 transition-colors`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Products Grid */}
        <section className="px-8 pb-16">
          <div className="max-w-6xl mx-auto">
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6'}>
              {products.map((product) => (
                <div key={product.id} className={viewMode === 'grid' ? 'group' : 'flex gap-6 bg-white/5 backdrop-blur-sm rounded-lg p-6'}>
                  <div className={viewMode === 'grid' ? 'relative aspect-[3/4] overflow-hidden rounded-lg mb-4' : 'relative w-48 h-48 flex-shrink-0 overflow-hidden rounded-lg'}>
                    <img 
                      src={product.imageUrl} 
                      alt={product.name} 
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" 
                    />
                    {product.isNew && (
                      <span className="absolute top-4 left-4 bg-white text-black px-2 py-1 text-xs font-light uppercase tracking-wider">
                        New
                      </span>
                    )}
                    {product.isSale && (
                      <span className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 text-xs font-light uppercase tracking-wider">
                        Sale
                      </span>
                    )}
                    <span className="absolute bottom-4 left-4 bg-black/70 text-white px-2 py-1 text-xs font-light uppercase tracking-wider backdrop-blur-sm">
                      Customizable
                    </span>
                    <button className="absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Heart className="w-5 h-5 text-white" />
                    </button>
                  </div>
                  
                  <div className={viewMode === 'grid' ? 'text-center' : 'flex-1 flex flex-col justify-center'}>
                    <p className="text-xs font-light tracking-wider text-white/60 uppercase mb-2">{product.category}</p>
                    <h3 className="text-lg font-light text-white mb-2">{product.name}</h3>
                    <div className="flex items-center gap-2 justify-center mb-2">
                      <span className="text-white font-light">{product.price}</span>
                      {product.originalPrice && (
                        <span className="text-white/60 line-through text-sm">{product.originalPrice}</span>
                      )}
                    </div>
                    <div className="flex flex-wrap gap-1 justify-center mb-3">
                      {product.personalizationOptions.map((option) => (
                        <span key={option} className="text-xs bg-white/10 text-white/80 px-2 py-1 rounded">
                          {option}
                        </span>
                      ))}
                    </div>
                    {viewMode === 'list' && (
                      <Button className="mt-4 bg-white text-black hover:bg-gray-100 font-light tracking-wide w-fit">
                        Customize
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Personalization;
