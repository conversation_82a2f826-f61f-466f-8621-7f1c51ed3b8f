import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const Perfumes: React.FC = () => {
  const perfumeCategories = [
    {
      title: "All Perfumes",
      description: "Explore our complete fragrance collection",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Iconic Scents",
      description: "Our signature fragrances that have become timeless classics",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Women's Perfumes",
      description: "Feminine fragrances from fresh florals to deep orientals",
      imageUrl: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Perfumes",
      description: "Sophisticated scents for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Cologne Perfumes",
      description: "Fresh, citrusy fragrances perfect for everyday wear",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Oud Perfumes",
      description: "Rich, exotic fragrances featuring precious oud wood",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Limited Editions",
      description: "Exclusive fragrances available for a limited time",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Travel Size",
      description: "Portable versions of your favorite fragrances",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="Perfumes"
      description="Discover our exquisite fragrance collection featuring iconic scents and exclusive creations. Each perfume tells a unique story through carefully selected notes and exceptional craftsmanship."
      backgroundImage="https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=2000&auto=format&fit=crop"
      categories={perfumeCategories}
      layout="image-grid"
    />
  );
};

export default Perfumes;
