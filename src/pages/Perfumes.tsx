import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Perfumes: React.FC = () => {
  const perfumeCategories = [
    {
      title: "All Perfumes",
      description: "Explore our complete fragrance collection",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Iconic Scents",
      description: "Our signature fragrances that have become timeless classics",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Women's Perfumes",
      description: "Feminine fragrances from fresh florals to deep orientals",
      imageUrl: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Perfumes",
      description: "Sophisticated scents for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Cologne Perfumes",
      description: "Fresh, citrusy fragrances perfect for everyday wear",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Oud Perfumes",
      description: "Rich, exotic fragrances featuring precious oud wood",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Limited Editions",
      description: "Exclusive fragrances available for a limited time",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Travel Size",
      description: "Portable versions of your favorite fragrances",
      imageUrl: "https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1541643600914-78b084683601?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Luxury Fragrances
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Perfume
                  <br />
                  Collections
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our exquisite fragrance collection featuring iconic scents and exclusive creations. Each perfume tells a unique story through carefully selected notes and exceptional craftsmanship.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Perfumes" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Fragrances
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Signature Scents
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {perfumeCategories.map((category) => (
              <Link
                key={category.title}
                to={`/products?category=Perfumes&subcategory=${encodeURIComponent(category.title)}`}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Perfumes;
