import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowR<PERSON>, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const About: React.FC = () => {
  const implementedFeatures = [
    {
      category: "Navigation & Pages",
      features: [
        "Complete menu navigation system",
        "Category pages for all main sections",
        "Responsive mobile navigation",
        "Breadcrumb navigation",
        "URL-based routing"
      ]
    },
    {
      category: "Product System",
      features: [
        "Product catalog with filtering",
        "Product detail pages",
        "Shopping cart functionality",
        "Product variants (colors, sizes)",
        "Product search and sorting"
      ]
    },
    {
      category: "Category Pages",
      features: [
        "Gifts collection page",
        "New arrivals showcase",
        "Bags & Wallets catalog",
        "Women's collection",
        "Men's collection",
        "Jewelry showcase",
        "Watches collection",
        "Perfumes catalog",
        "Travel & Home items",
        "Services information"
      ]
    },
    {
      category: "User Experience",
      features: [
        "Responsive design (mobile-first)",
        "Smooth animations and transitions",
        "Interactive shopping cart",
        "Product image galleries",
        "Category filtering system"
      ]
    }
  ];

  const categoryPages = [
    { name: "Gifts", path: "/gifts", description: "Luxury gifts for every occasion" },
    { name: "New Arrivals", path: "/new", description: "Latest collections and seasonal pieces" },
    { name: "Bags & Wallets", path: "/bags-and-wallets", description: "Premium leather goods and accessories" },
    { name: "Women", path: "/women", description: "Complete women's luxury collection" },
    { name: "Men", path: "/men", description: "Sophisticated men's accessories" },
    { name: "Jewelry", path: "/jewelry", description: "Fine jewelry and precious stones" },
    { name: "Watches", path: "/watches", description: "Luxury timepieces and collections" },
    { name: "Perfumes", path: "/perfumes", description: "Exclusive fragrances and scents" },
    { name: "Travel & Home", path: "/travel-and-home", description: "Travel accessories and home decor" },
    { name: "Services", path: "/services", description: "Personalization and customer services" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-light tracking-wide mb-4">
              LORIYA E-Commerce System
            </h1>
            <p className="text-lg font-light text-white/80 max-w-2xl mx-auto">
              A complete luxury e-commerce platform with comprehensive navigation, product management, and shopping functionality.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Implementation Status */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-light text-gray-900 mb-4">Implementation Status</h2>
            <Badge variant="secondary" className="bg-green-100 text-green-800 px-4 py-2">
              ✅ Fully Implemented & Functional
            </Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {implementedFeatures.map((section) => (
              <Card key={section.category} className="border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg font-light text-gray-900">
                    {section.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {section.features.map((feature) => (
                      <li key={feature} className="flex items-center gap-2 text-sm text-gray-600">
                        <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Category Pages */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-light text-gray-900 mb-4">Category Pages</h2>
            <p className="text-gray-600 font-light max-w-2xl mx-auto">
              Explore all the category pages that have been implemented with full navigation integration.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoryPages.map((page) => (
              <Card key={page.path} className="border-gray-200 hover:border-gray-300 transition-colors">
                <CardContent className="p-6">
                  <h3 className="text-lg font-light text-gray-900 mb-2">{page.name}</h3>
                  <p className="text-sm text-gray-600 font-light mb-4">{page.description}</p>
                  <Link to={page.path}>
                    <Button variant="outline" size="sm" className="w-full font-light">
                      Visit Page
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Product System */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-light text-gray-900 mb-4">Product System</h2>
            <p className="text-gray-600 font-light max-w-2xl mx-auto">
              Complete product management system with filtering, cart functionality, and detailed product pages.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="border-gray-200">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-light text-gray-900 mb-2">Product Catalog</h3>
                <p className="text-sm text-gray-600 font-light mb-4">
                  Browse products with advanced filtering and sorting options.
                </p>
                <Link to="/products">
                  <Button className="bg-black text-white hover:bg-gray-800 font-light">
                    View Products
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="border-gray-200">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-light text-gray-900 mb-2">Sample Product</h3>
                <p className="text-sm text-gray-600 font-light mb-4">
                  See a detailed product page with variants and cart functionality.
                </p>
                <Link to="/products/1">
                  <Button className="bg-black text-white hover:bg-gray-800 font-light">
                    View Sample
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="border-gray-200">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-light text-gray-900 mb-2">Category Filter</h3>
                <p className="text-sm text-gray-600 font-light mb-4">
                  Filter products by category using URL parameters.
                </p>
                <Link to="/products?category=Jewelry">
                  <Button className="bg-black text-white hover:bg-gray-800 font-light">
                    View Jewelry
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Technical Features */}
        <div className="text-center">
          <h2 className="text-3xl font-light text-gray-900 mb-4">Technical Features</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
            {[
              "React + TypeScript",
              "Responsive Design",
              "State Management",
              "URL Routing",
              "Component Library",
              "Shopping Cart",
              "Product Filtering",
              "Mobile Navigation"
            ].map((tech) => (
              <Badge key={tech} variant="outline" className="p-2 font-light">
                {tech}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
