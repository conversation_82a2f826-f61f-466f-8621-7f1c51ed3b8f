import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Watches: React.FC = () => {
  const watchCategories = [
    {
      title: "All Watches",
      description: "Browse our complete collection of luxury timepieces",
      imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Escale Collection",
      description: "Travel-inspired timepieces with world time complications",
      imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Tambour Collection",
      description: "Our iconic drum-shaped case design in various interpretations",
      imageUrl: "https://images.unsplash.com/photo-1547996160-81dfa63595aa?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Tambour Convergence",
      description: "Smart luxury watches combining tradition with innovation",
      imageUrl: "https://images.unsplash.com/photo-1434056886845-dac89ffe9b56?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Tambour Taiko",
      description: "Inspired by Japanese drumming, featuring unique complications",
      imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Original Tambour",
      description: "The classic collection that started our watchmaking journey",
      imageUrl: "https://images.unsplash.com/photo-1547996160-81dfa63595aa?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Objects of Time",
      description: "Artistic timepieces that blur the line between watch and art",
      imageUrl: "https://images.unsplash.com/photo-1434056886845-dac89ffe9b56?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Women's Watches",
      description: "Elegant timepieces designed for the sophisticated woman",
      imageUrl: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1524592094714-0f0654e20314?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Luxury Timepieces
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Watch
                  <br />
                  Collections
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Explore our exceptional collection of luxury timepieces. From classic elegance to innovative complications, discover watches that represent the pinnacle of Swiss craftsmanship.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Watches" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Watches
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Precision & Style
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {watchCategories.map((category) => (
              <Link
                key={category.title}
                to={`/products?category=Watches&subcategory=${encodeURIComponent(category.title)}`}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Watches;
