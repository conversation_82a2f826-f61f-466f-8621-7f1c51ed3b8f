import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Heart, User, ShoppingBag, Shirt, Menu, ChevronRight, ArrowLeft, X, Pause, ChevronDown } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { AuthSheet } from "@/components/AuthDialog";

interface MenuItem {
  title: string;
  subcategories?: MenuItem[];
  layout?: string;
  href?: string;
}

const Index = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [contactName, setContactName] = useState("");
  const [contactEmail, setContactEmail] = useState("");
  const [contactMessage, setContactMessage] = useState("");

  const menuItems: MenuItem[] = [
    {
      title: "Gifts",
      subcategories: [
        { title: "Gifts for Her", href: "/gifts-for-her" },
        { title: "Gifts for Home", href: "/gifts-for-home" },
        { title: "Gifts for Babies", href: "/gifts-for-babies" },
        { title: "Personalization", href: "/personalization" },
      ],
    },
    {
      title: "New",
      layout: "image-grid-variant",
      subcategories: [
        {
          title: "For Women",
          layout: "image-grid-variant",
          subcategories: [
            { title: "New Arrivals" },
            { title: "Pre-Fall 2025 Collection" },
            { title: "Spring 2025 Collection" },
            { title: "New Formal Collection" },
            { title: "Spring-Summer 2025 Show" },
          ],
        },
        {
          title: "For Men",
          layout: "image-grid-variant",
          subcategories: [
            { title: "New Arrivals" },
            { title: "Pre-Fall 2025 Collection" },
            { title: "Spring 2025 Collection" },
            { title: "New Formal Collection" },
            { title: "Spring-Summer 2025 Show" },
          ],
        },
      ],
    },
    {
      title: "Bags and Wallets",
      subcategories: [
        {
          title: "Women's Bags",
          subcategories: [
            { title: "Iconic Handbags" },
            { title: "All Handbags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "Capucines" },
          ],
        },
        {
          title: "Men's Bags",
          subcategories: [
            { title: "Iconic Bags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "All Bags" },
          ],
        },
        { title: "Women's Small Leather Goods" },
        { title: "Men's Small Leather Goods" },
        { title: "Personalization" },
      ],
    },
    {
      title: "Women",
      subcategories: [
        {
          title: "Handbags",
          layout: 'image-grid',
          subcategories: [
            { title: "Iconic Handbags" },
            { title: "All Handbags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "Capucines" },
          ],
        },
        {
          title: "Wallets",
          layout: 'image-grid',
          subcategories: [
            { title: "All Small Leather Goods" },
            { title: "New In" },
            { title: "Wallets on Chain and Nano Bags" },
            { title: "Card and Key Holders" },
            { title: "Compact and Long Wallets" },
            { title: "LORIYA Essentials" },
          ],
        },
        {
          title: "Shoes",
          layout: 'image-grid',
          subcategories: [
            { title: "All Shoes" },
            { title: "Sneakers" },
            { title: "Boots & Booties" },
            { title: "Loafers and Ballerinas" },
            { title: "Mules and Slides" },
            { title: "Sandals and Espadrilles" },
            { title: "Pumps" },
          ],
        },
        { 
          title: "Accessories",
          layout: 'image-grid',
          subcategories: [
            { title: "Key Holders & Bag Charms" },
            { title: "Sunglasses" },
            { title: "Belts" },
            { title: "Silk Squares and Bandeaus" },
            { title: "Scarves" },
            { title: "Hats and Gloves" },
            { title: "Hair Accessories" },
            { title: "Home Accessories" },
            { title: "Tech Accessories" },
          ]
        },
        { 
          title: "Fashion Jewelry",
          layout: 'image-grid',
          subcategories: [
            { title: "All Fashion Jewelry" },
            { title: "Bracelets" },
            { title: "Rings" },
            { title: "Earrings" },
            { title: "Necklaces and Pendants" },
            { title: "Brooches" },
          ]
        },
        { 
          title: "Ready-to-Wear",
          layout: 'image-grid',
          subcategories: [
            { title: "All Ready-to-Wear" },
            { title: "Tops" },
            { title: "Skirts and Shorts" },
            { title: "Swimwear" },
            { title: "Dresses" },
            { title: "Denim" },
            { title: "Pants" },
            { title: "Knitwear" },
            { title: "Coats and Jackets" },
          ]
        },
      ],
    },
    {
      title: "Men",
      subcategories: [
        {
          title: "Bags",
          layout: 'image-grid',
          subcategories: [
            { title: "Iconic Bags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "All Bags" },
          ],
        },
        {
          title: "Wallets",
          layout: 'image-grid',
          subcategories: [
            { title: "All Wallets" },
            { title: "New In" },
            { title: "Compact and Long Wallets" },
            { title: "Card Holders" },
            { title: "Pouches" },
            { title: "Mini Bags" },
          ]
        },
        { title: "Shoes",
          layout: 'image-grid',
          subcategories: [
            { title: "All Shoes" },
            { title: "Sneakers" },
            { title: "Loafers and Moccasins" },
            { title: "Buckles and Lace-Ups" },
            { title: "Boots" },
            { title: "Sandals" },
          ],
        },
        { title: "Accessories",
          layout: 'image-grid',
          subcategories: [
            { title: "Belts" },
            { title: "Sunglasses" },
            { title: "Key Holders, Key Chains and Bag Charms" },
            { title: "Hats, Beanies and Gloves" },
            { title: "Scarves" },
            { title: "Ties and Pocket Squares" },
            { title: "Silk and Bandanas" },
            { title: "Home Textile" },
            { title: "Tech Accessories" },
          ]
        },
        { 
          title: "Fashion Jewelry",
          layout: 'image-grid',
          subcategories: [
            { title: "All Fashion Jewelry" },
            { title: "Silver Collection" },
            { title: "Necklaces and Pendants" },
            { title: "Bracelets" },
            { title: "Rings" },
            { title: "Earrings" },
          ]
        },
        { 
          title: "Ready-to-Wear",
          layout: 'image-grid',
          subcategories: [
            { title: "All Ready-to-Wear" },
            { title: "T-Shirts and Polos" },
            { title: "Swimwear" },
            { title: "Pants" },
            { title: "Jeans" },
            { title: "Shirts" },
            { title: "Knitwear and Sweatshirts" },
            { title: "Coats and Outerwear" },
            { title: "Blazers and Jackets" },
          ]
        },
      ],
    },
    {
      title: "Jewelry",
      subcategories: [
        {
          title: "Collections",
          layout: 'image-grid',
          subcategories: [
            { title: "Color Blossom" },
            { title: "Idylle Blossom" },
            { title: "Le Damier de LORIYA" },
            { title: "Empreinte Collection" },
            { title: "Les Gastons LORIYA" },
            { title: "LORIYA Diamonds" },
            { title: "Silver Lockit" },
            { title: "Other Jewelry Collections" },
          ],
        },
        {
          title: "Categories",
          layout: 'image-grid',
          subcategories: [
            { title: "All Fine Jewelry" },
            { title: "Bracelets" },
            { title: "Earrings" },
            { title: "Necklaces and Pendants" },
            { title: "Rings" },
            { title: "Engagement Rings and Wedding Bands" },
            { title: "Men's Jewelry" },
          ],
        },
      ],
    },
    {
      title: "Watches",
      subcategories: [
        { title: "All Watches" },
        {
          title: "Watch Collections",
          layout: 'image-grid',
          subcategories: [
            { title: "Escale" },
            { title: "Tambour" },
            { title: "Tambour Convergence" },
            { title: "Tambour Taiko" },
            { title: "Original Tambour" },
            { title: "Objects of Time" },
          ],
        },
      ],
    },
    { 
      title: "Perfumes",
      subcategories: [
        { 
          title: "Iconic Scents",
          layout: 'image-grid',
          subcategories: [
            { title: "Imagination" },
            { title: "Attrape-Rêves" },
            { title: "Spell On You" },
            { title: "L'Immensité" },
            { title: "Ombre Nomade" },
          ]
        },
        { 
          title: "Categories",
          layout: 'image-grid',
          subcategories: [
            { title: "All Perfumes" },
            { title: "Women's Perfumes" },
            { title: "Men's Perfumes" },
            { title: "Cologne Perfumes" },
            { title: "Oud Perfumes" },
          ]
        },
      ]
    },
    { 
      title: "Travel and Home",
      subcategories: [
        {
          title: "Travel Bags and Rolling Luggage",
          layout: 'image-grid',
          subcategories: [
            { title: "Rolling Luggage" },
            { title: "Travel Bags" },
            { title: "Travel Accessories" },
            { title: "Personalization" },
          ],
        },
        {
          title: "Home and Art of Dining",
          layout: 'image-grid',
          subcategories: [
            { title: "Furniture" },
            { title: "Decoration" },
            { title: "Art of Dining" },
            { title: "Home Textiles" },
          ]
        },
      ]
    },
    { 
      title: "Services",
      subcategories: [
        { 
          title: "Personalization",
          layout: 'image-grid',
          subcategories: [
            { title: "Mon Monogram" },
            { title: "LORIYA & I Alma Personalization" },
            { title: "Bags and Small Leather Goods" },
            { title: "Travel" },
            { title: "Jewelry" },
            { title: "Perfumes" }
          ]
        },
        { title: "LORIYA Repairs" }
      ]
    },
  ];

  const loriyaServices = [
    {
      title: "Book An Appointment",
      description: "Enjoy priority access to the boutique of your choice. When you arrive, your Client Advisor will guide you through a hand-picked selection.",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=2369&auto=format&fit=crop",
      alt: "A client advisor assisting a customer in a luxury boutique."
    },
    {
      title: "Complimentary Shipping & Returns",
      description: "Enjoy shipping in 1-2 business days, complimentary returns and streamlined exchanges with no extra cost.",
      imageUrl: "https://images.unsplash.com/photo-1617128038662-de2954a4857d?q=80&w=2370&auto=format&fit=crop",
      alt: "Luxury branded shopping bags and boxes."
    },
    {
      title: "Personalisation",
      description: "Emboss select bags, luggage, and leather accessories with your initials to create a truly unique piece.",
      imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=2370&auto=format&fit=crop",
      alt: "Tools for personalising leather goods."
    }
  ];

  const [menuStack, setMenuStack] = useState<{ items: MenuItem[]; title: string; layout?: string; }[]>(() => [{ items: menuItems, title: "Menu" }]);

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Newsletter signup:", email);
    setEmail("");
    toast.success("Thank you for subscribing!");
  };

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Contact form submitted:", { name: contactName, email: contactEmail, message: contactMessage });
    toast.success("Your message has been sent!");
    setContactName("");
    setContactEmail("");
    setContactMessage("");
  };

  const handleSheetOpenChange = (open: boolean) => {
    setIsMenuOpen(open);
    if (!open) {
      setTimeout(() => setMenuStack([{ items: menuItems, title: "Menu" }]), 300);
    }
  };

  const backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721";

  const activeMenu = menuStack[menuStack.length - 1];

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start">
              <Sheet open={isMenuOpen} onOpenChange={handleSheetOpenChange}>
                <SheetTrigger asChild>
                  <button
                    className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"
                  >
                    <Menu size={20} />
                    <span className="text-sm font-light tracking-wide">MENU</span>
                  </button>
                </SheetTrigger>
                <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="relative h-full overflow-x-hidden">
                    {/* Main Menu */}
                    <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "-translate-x-full" : "translate-x-0"}`}>
                      <div className="flex flex-col h-full pt-24">
                        <div className="flex-grow space-y-4 pb-8 overflow-y-auto px-8">
                          {menuItems.map((item) => (
                            <div key={item.title}>
                              <a
                                href="#"
                                className="flex items-center justify-between w-full text-2xl font-light text-white hover:text-gray-300 transition-colors"
                                onClick={(e) => {
                                  if (item.subcategories) {
                                    e.preventDefault();
                                    setMenuStack(stack => [...stack, {items: item.subcategories, title: item.title, layout: item.layout}]);
                                  }
                                }}
                              >
                                <span>{item.title}</span>
                                {item.subcategories && (
                                  <ChevronRight className="w-5 h-5" />
                                )}
                              </a>
                            </div>
                          ))}
                          <div>
                            <div className="text-2xl font-light text-white">The Maison LORIYA</div>
                          </div>
                        </div>
                        <div className="flex-shrink-0 pt-4 px-8 pb-12">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                            <div className="mt-4 space-y-2 text-sm font-light">
                              <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                                ADDRESS <span>→</span>
                              </a>
                              <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                                OPENING HOURS <span>→</span>
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Sub Menu Container */}
                    <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "translate-x-0" : "translate-x-full"}`}>
                      {menuStack.length > 1 && (
                        <div className="flex flex-col h-full">
                          {/* Header */}
                          <div className="pt-24 pb-6 px-8 flex-shrink-0">
                            <button
                              onClick={() => setMenuStack(stack => stack.slice(0, -1))}
                              className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider text-sm font-light"
                            >
                              <ArrowLeft className="w-4 h-4" />
                              <span>Back</span>
                            </button>

                            <h2 className="text-3xl font-light text-white mt-8 mb-6">{activeMenu.title}</h2>
                          </div>

                          {/* Scrollable Content + Footer */}
                          <div className="overflow-y-auto px-8 pb-12 flex-grow">
                            {/* Content */}
                            <div>
                              {activeMenu.layout === 'image-grid' ? (
                                <div>
                                  <div className="mb-8">
                                    <p className="text-sm font-light tracking-widest text-white/80 uppercase mb-4">New Arrivals</p>
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <a href="#" className="group">
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <p className="text-xs font-light text-white/80 group-hover:text-white">Pre-Fall 2025 Collection</p>
                                        </a>
                                      </div>
                                      <div>
                                        <a href="#" className="group">
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <p className="text-xs font-light text-white/80 group-hover:text-white">Spring 2025 Collection</p>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                                    {activeMenu.items.map((subItem) => (
                                      <a
                                        key={subItem.title}
                                        href="#"
                                        className="group"
                                        onClick={(e) => {
                                          if (subItem.subcategories) {
                                            e.preventDefault();
                                            setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                          }
                                        }}
                                      >
                                        <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                          <p className="text-white/40 text-xs">Image</p>
                                        </div>
                                        <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                      </a>
                                    ))}
                                  </div>
                                </div>
                              ) : activeMenu.layout === 'image-grid-variant' ? (
                                <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                                  {activeMenu.items.map((subItem) => (
                                    <a
                                      key={subItem.title}
                                      href="#"
                                      className="group"
                                      onClick={(e) => {
                                        if (subItem.subcategories) {
                                          e.preventDefault();
                                          setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                        }
                                      }}
                                    >
                                      <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                        <p className="text-white/40 text-xs">Image</p>
                                      </div>
                                      <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                    </a>
                                  ))}
                                </div>
                              ) : (
                                <div className="space-y-3">
                                  {activeMenu.items.map((subItem) => (
                                    subItem.href ? (
                                      <Link
                                        key={subItem.title}
                                        to={subItem.href}
                                        className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                                      >
                                        <span>{subItem.title}</span>
                                      </Link>
                                    ) : (
                                      <a
                                        key={subItem.title}
                                        href="#"
                                        className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                                        onClick={(e) => {
                                          if (subItem.subcategories) {
                                            e.preventDefault();
                                            setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                          }
                                        }}
                                      >
                                        <span>{subItem.title}</span>
                                        {subItem.subcategories && <ChevronRight className="w-5 h-5" />}
                                      </a>
                                    )
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* Footer */}
                            <div className="pt-4">
                              <Separator className="mb-4 bg-white/20" />
                              <div className="space-y-1 text-sm font-light">
                                <p className="text-white/80">Can we help you?</p>
                                <p className="text-white tracking-wider">+1.866.LORIYA</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            <div className="text-center font-serif text-2xl font-light tracking-wider text-white">
              LORIYA
            </div>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="text-sm font-light tracking-wide hover:text-gray-200 transition-colors hidden md:block">
                    Contact Us
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Contact Us</h2>
                      </div>
                      <div className="flex-grow py-8 overflow-y-auto">
                        <form onSubmit={handleContactSubmit} className="space-y-6">
                            <div>
                                <label htmlFor="contact-name" className="block text-sm font-light text-white/80 mb-2">Name</label>
                                <Input 
                                    id="contact-name"
                                    type="text"
                                    placeholder="Your name"
                                    value={contactName}
                                    onChange={(e) => setContactName(e.target.value)}
                                    className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full"
                                    required
                                />
                            </div>
                            <div>
                                <label htmlFor="contact-email" className="block text-sm font-light text-white/80 mb-2">Email</label>
                                <Input
                                    id="contact-email"
                                    type="email"
                                    placeholder="Your email"
                                    value={contactEmail}
                                    onChange={(e) => setContactEmail(e.target.value)}
                                    className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full"
                                    required
                                />
                            </div>
                            <div>
                                <label htmlFor="contact-message" className="block text-sm font-light text-white/80 mb-2">Message</label>
                                <Textarea
                                    id="contact-message"
                                    placeholder="How can we help you?"
                                    value={contactMessage}
                                    onChange={(e) => setContactMessage(e.target.value)}
                                    className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full min-h-[120px]"
                                    required
                                />
                            </div>
                            <Button 
                                type="submit"
                                className="w-full bg-white text-black hover:bg-gray-100 px-8 py-3 font-light tracking-wide"
                            >
                                Send Message
                            </Button>
                        </form>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Wishlist</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your wishlist is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <AuthSheet>
                <button className="hover:text-gray-200 transition-colors">
                  <User size={20} />
                </button>
              </AuthSheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                      0
                    </span>
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your cart is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="px-8 py-16 md:py-24">
          <div className="max-w-6xl mx-auto">
            <div className="text-center">
              <div className="space-y-8">
                <div className="space-y-4">
                  <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                    Spring Collection 2024
                  </p>
                  <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                    Timeless
                    <br />
                    Elegance
                  </h1>
                  <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                    Discover our carefully curated collection of contemporary essentials, 
                    crafted with attention to detail and sustainable practices.
                  </p>
                </div>
                <div className="flex justify-center pt-4">
                  <a href="#" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                    Shop Now
                    <span className="block w-full h-px bg-white mt-1"></span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Collections */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <p className="text-sm font-light tracking-widest text-white/70 uppercase">
                Featured Collections
              </p>
              <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                Curated for You
              </h2>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { title: "Women", subtitle: "Handbags", imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop" },
                { title: "Men", subtitle: "Bags", imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=2370&auto=format&fit=crop" },
                { title: "Kids", subtitle: "Playwear", imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop" },
                { title: "Accessories", subtitle: "Essentials", imageUrl: "https://images.unsplash.com/photo-1526328828355-69b068d1c72a?q=80&w=2370&auto=format&fit=crop" }
              ].map((collection) => (
                <div key={collection.title} className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg">
                  <img src={collection.imageUrl} alt={collection.title} className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute inset-0 flex flex-col justify-between p-4 text-white sm:p-6">
                      <div className="text-right">
                          <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                              {collection.title}
                          </span>
                      </div>
                      <div className="text-center">
                          <h3 className="font-serif text-2xl font-light mb-2">{collection.subtitle}</h3>
                          <a href="#" className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 hover:bg-white hover:text-black">
                              Shop The Collection
                          </a>
                      </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="px-8 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/70 uppercase">
                  Our Philosophy
                </p>
                <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                  Crafted with Purpose
                </h2>
              </div>
              <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                We believe in creating garments that transcend seasons and trends. 
                Each piece is thoughtfully designed and ethically made, using only 
                the finest sustainable materials.
              </p>
              <div className="flex justify-center space-x-12 pt-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                    <Shirt className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-sm font-light text-white/80">Sustainable Materials</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                    <ShoppingBag className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-sm font-light text-white/80">Ethical Production</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Loriya Services Section */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                Loriya Services
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-12 text-white">
              {loriyaServices.map((service) => (
                <div key={service.title}>
                  <div className="relative mb-6 group">
                    <div className="aspect-square bg-white/10 rounded-lg overflow-hidden border border-white/20">
                      <img src={service.imageUrl} alt={service.alt} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
                    </div>
                    <button className="absolute top-4 right-4 bg-black/30 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center cursor-pointer hover:bg-black/50 transition-colors">
                      <Pause className="w-5 h-5 text-white" />
                    </button>
                  </div>
                  <div className="text-center px-2">
                    <h3 className="text-sm font-light tracking-[0.2em] uppercase mb-4">{service.title}</h3>
                    <p className="text-sm font-light text-white/80 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="px-8 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                  Stay Connected
                </h2>
                <p className="text-lg font-light text-white/80">
                  Be the first to know about new collections and exclusive offers.
                </p>
              </div>
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm flex-1"
                  required
                />
                <Button 
                  type="submit"
                  className="bg-white text-black hover:bg-gray-100 px-8 py-2 font-light tracking-wide"
                >
                  Subscribe
                </Button>
              </form>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="px-8 py-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-8">
              <div className="font-serif text-2xl font-light tracking-wider text-white">
                LORIYA
              </div>
              <div className="flex justify-center space-x-8 text-sm font-light text-white/70">
                <a href="#" className="hover:text-white transition-colors">Collections</a>
                <a href="#" className="hover:text-white transition-colors">About</a>
                <a href="#" className="hover:text-white transition-colors">Contact</a>
                <a href="#" className="hover:text-white transition-colors">Size Guide</a>
              </div>
              <div className="pt-8 border-t border-white/20">
                <p className="text-xs font-light text-white/50">
                  © 2024 Loriya. All rights reserved.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
