import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Gifts: React.FC = () => {
  const giftCategories = [
    {
      title: "Gifts for Her",
      description: "Elegant handbags, jewelry, and accessories that celebrate her unique style",
      href: "/products?category=Women"
    },
    {
      title: "Gifts for Home",
      description: "Luxurious home accessories and decorative pieces for sophisticated living",
      href: "/travel-and-home"
    },
    {
      title: "Gifts for Babies",
      description: "Precious keepsakes and soft accessories for the little ones",
      href: "/products?category=Gifts&subcategory=Gifts for Babies"
    },
    {
      title: "Personalization",
      description: "Make it uniquely theirs with custom monogramming and engraving services",
      href: "/services"
    },
    {
      title: "Gift Cards",
      description: "The perfect gift when you want to let them choose their own luxury piece",
      href: "/products?category=Gifts&subcategory=Gift Cards"
    },
    {
      title: "Gift Wrapping",
      description: "Elegant presentation with our signature gift wrapping service",
      href: "/services"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Luxury Gifts
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Perfect
                  <br />
                  Gifts
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover the perfect luxury gifts for every special occasion. From timeless handbags to exquisite jewelry, find something extraordinary for the people you love.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Gifts
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gift Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Gift Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              For Every Occasion
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {giftCategories.map((category) => (
              <Link
                key={category.title}
                to={category.href}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="px-8 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-8 md:p-12">
            <h2 className="font-serif text-2xl md:text-3xl font-light text-white mb-4">
              Need Help Finding the Perfect Gift?
            </h2>
            <p className="text-white/80 font-light mb-6 max-w-2xl mx-auto">
              Our personal shopping experts are here to help you discover the perfect pieces for your loved ones. Book a complimentary consultation today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/services">
                <button className="bg-white text-black hover:bg-gray-100 px-8 py-3 font-light tracking-wide transition-colors">
                  Book Consultation
                </button>
              </Link>
              <Link to="/products">
                <button className="border border-white text-white hover:bg-white hover:text-black px-8 py-3 font-light tracking-wide transition-colors">
                  Browse All Gifts
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Gifts;
