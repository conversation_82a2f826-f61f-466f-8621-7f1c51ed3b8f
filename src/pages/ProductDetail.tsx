import React from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ProductDetail as ProductDetailComponent } from '@/components/products/ProductDetail';
import { ProductGrid } from '@/components/products/ProductGrid';
import { useProducts } from '@/hooks/useProducts';

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getProductById, getProductsByCategory } = useProducts();

  const product = id ? getProductById(id) : undefined;

  if (!product) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-light text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 font-light mb-6">
            The product you're looking for doesn't exist or has been removed.
          </p>
          <Button
            onClick={() => navigate('/products')}
            className="bg-black text-white hover:bg-gray-800 font-light tracking-wider"
          >
            Browse All Products
          </Button>
        </div>
      </div>
    );
  }

  const relatedProducts = getProductsByCategory(product.category)
    .filter(p => p.id !== product.id)
    .slice(0, 4);

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: product.category, href: `/products?category=${encodeURIComponent(product.category)}` },
  ];

  if (product.subcategory) {
    breadcrumbItems.push({
      label: product.subcategory,
      href: `/products?category=${encodeURIComponent(product.category)}&subcategory=${encodeURIComponent(product.subcategory)}`
    });
  }

  breadcrumbItems.push({ label: product.name, href: '' });

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2 font-light"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>

            {/* Breadcrumb */}
            <nav className="hidden md:flex items-center space-x-2 text-sm font-light">
              {breadcrumbItems.map((item, index) => (
                <React.Fragment key={index}>
                  {index > 0 && <ChevronRight className="h-4 w-4 text-gray-400" />}
                  {item.href && index < breadcrumbItems.length - 1 ? (
                    <Link
                      to={item.href}
                      className="text-gray-600 hover:text-gray-900 transition-colors"
                    >
                      {item.label}
                    </Link>
                  ) : (
                    <span className={index === breadcrumbItems.length - 1 ? 'text-gray-900' : 'text-gray-600'}>
                      {item.label}
                    </span>
                  )}
                </React.Fragment>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Product Detail */}
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <ProductDetailComponent product={product} />
      </div>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <>
          <Separator />
          <div className="container mx-auto px-4 py-12">
            <div className="text-center mb-8">
              <h2 className="text-2xl lg:text-3xl font-light text-gray-900 mb-4">
                You May Also Like
              </h2>
              <p className="text-gray-600 font-light max-w-2xl mx-auto">
                Discover more products from our {product.category.toLowerCase()} collection
              </p>
            </div>

            <ProductGrid 
              products={relatedProducts}
              columns={{ mobile: 2, tablet: 3, desktop: 4 }}
            />

            <div className="text-center mt-8">
              <Link to={`/products?category=${encodeURIComponent(product.category)}`}>
                <Button
                  variant="outline"
                  className="font-light tracking-wider px-8"
                >
                  View All {product.category}
                </Button>
              </Link>
            </div>
          </div>
        </>
      )}

      {/* Product Care & Services (Future Enhancement) */}
      <div className="bg-gray-50">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <h3 className="font-light text-lg mb-2">Free Shipping</h3>
              <p className="text-sm text-gray-600 font-light">
                Complimentary shipping on all orders over $500
              </p>
            </div>

            <div>
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-light text-lg mb-2">Authenticity Guarantee</h3>
              <p className="text-sm text-gray-600 font-light">
                Every product comes with a certificate of authenticity
              </p>
            </div>

            <div>
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="font-light text-lg mb-2">Personalization</h3>
              <p className="text-sm text-gray-600 font-light">
                Make it uniquely yours with our personalization services
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
