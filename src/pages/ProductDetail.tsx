import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart, User, ShoppingBag, Minus, Plus, Star, ArrowLeft } from "lucide-react";
import { Link, useParams, useNavigate } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { AuthSheet } from "@/components/AuthDialog";
import { MainMenu } from "@/components/MainMenu";

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState("M");
  const [selectedColor, setSelectedColor] = useState("Black");
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // Mock product data - in real app this would come from API based on id
  const product = {
    id: 1,
    name: "Silk Scarf Collection",
    price: "$285",
    originalPrice: "$350",
    description: "Discover our carefully curated collection of contemporary essentials, crafted with attention to detail and sustainable practices. This luxurious silk scarf features an exclusive pattern inspired by the timeless elegance of Parisian fashion.",
    category: "Accessories",
    isNew: true,
    isSale: true,
    rating: 4.8,
    reviews: 127,
    images: [
      "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2370&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?q=80&w=2305&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1511499767150-a48a237f0083?q=80&w=2080&auto=format&fit=crop"
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    colors: ["Black", "Navy", "Burgundy", "Cream"],
    features: [
      "100% Pure Silk",
      "Hand-finished edges",
      "Exclusive LORIYA pattern",
      "Made in France",
      "Dry clean only"
    ],
    inStock: true
  };

  const backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721";

  const handleQuantityChange = (change: number) => {
    setQuantity(Math.max(1, quantity + change));
  };

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start">
              <button
                onClick={() => navigate(-1)}
                className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span className="text-sm font-light tracking-wide">BACK</span>
              </button>
            </div>
            <div className="text-center">
              <Link to="/" className="font-serif text-2xl font-light tracking-wider text-white hover:text-gray-200 transition-colors">
                LORIYA
              </Link>
            </div>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Wishlist</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your wishlist is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <AuthSheet>
                <button className="hover:text-gray-200 transition-colors">
                  <User size={20} />
                </button>
              </AuthSheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                      0
                    </span>
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your cart is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Product Detail Section */}
        <section className="px-8 py-16">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Product Images */}
              <div className="space-y-4">
                {/* Main Image */}
                <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border border-white/20">
                  <img 
                    src={product.images[activeImageIndex]} 
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                  {product.isNew && (
                    <div className="absolute top-4 left-4 bg-white text-black px-3 py-1 text-xs font-light uppercase tracking-wider rounded">
                      New
                    </div>
                  )}
                  {product.isSale && (
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 text-xs font-light uppercase tracking-wider rounded">
                      Sale
                    </div>
                  )}
                </div>
                
                {/* Thumbnail Images */}
                <div className="grid grid-cols-4 gap-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveImageIndex(index)}
                      className={`aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border transition-all ${
                        activeImageIndex === index ? 'border-white/60' : 'border-white/20 hover:border-white/40'
                      }`}
                    >
                      <img 
                        src={image} 
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                {/* Breadcrumb */}
                <div className="text-sm font-light text-white/60 uppercase tracking-wider">
                  <Link to="/" className="hover:text-white/80">Home</Link>
                  <span className="mx-2">/</span>
                  <Link to="/gifts-for-her" className="hover:text-white/80">{product.category}</Link>
                  <span className="mx-2">/</span>
                  <span className="text-white/80">{product.name}</span>
                </div>

                {/* Product Title */}
                <div>
                  <h1 className="font-serif text-3xl md:text-4xl font-light text-white mb-2">
                    {product.name}
                  </h1>
                  <p className="text-sm font-light tracking-wider text-white/60 uppercase">
                    {product.category}
                  </p>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`w-4 h-4 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-white/30'}`} 
                      />
                    ))}
                  </div>
                  <span className="text-white/80 text-sm font-light">
                    {product.rating} ({product.reviews} reviews)
                  </span>
                </div>

                {/* Price */}
                <div className="flex items-center gap-3">
                  <span className="text-2xl font-light text-white">{product.price}</span>
                  {product.originalPrice && (
                    <span className="text-lg text-white/60 line-through">{product.originalPrice}</span>
                  )}
                  {product.isSale && (
                    <span className="bg-red-500 text-white px-2 py-1 text-xs font-light uppercase tracking-wider rounded">
                      Save {Math.round(((parseFloat(product.originalPrice?.replace('$', '') || '0') - parseFloat(product.price.replace('$', ''))) / parseFloat(product.originalPrice?.replace('$', '') || '1')) * 100)}%
                    </span>
                  )}
                </div>

                {/* Description */}
                <p className="text-white/90 font-light leading-relaxed">
                  {product.description}
                </p>

                {/* Features */}
                <div>
                  <h3 className="text-lg font-light text-white mb-3">Features</h3>
                  <ul className="space-y-1">
                    {product.features.map((feature, index) => (
                      <li key={index} className="text-white/80 font-light text-sm flex items-center">
                        <span className="w-1 h-1 bg-white/60 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Size Selection */}
                <div>
                  <h3 className="text-lg font-light text-white mb-3">Size</h3>
                  <div className="flex gap-2">
                    {product.sizes.map((size) => (
                      <button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={`px-4 py-2 border backdrop-blur-sm rounded transition-all ${
                          selectedSize === size
                            ? 'bg-white/20 border-white/60 text-white'
                            : 'bg-white/10 border-white/30 text-white/80 hover:bg-white/15 hover:border-white/40'
                        }`}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Color Selection */}
                <div>
                  <h3 className="text-lg font-light text-white mb-3">Color</h3>
                  <div className="flex gap-2">
                    {product.colors.map((color) => (
                      <button
                        key={color}
                        onClick={() => setSelectedColor(color)}
                        className={`px-4 py-2 border backdrop-blur-sm rounded transition-all ${
                          selectedColor === color
                            ? 'bg-white/20 border-white/60 text-white'
                            : 'bg-white/10 border-white/30 text-white/80 hover:bg-white/15 hover:border-white/40'
                        }`}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Quantity */}
                <div>
                  <h3 className="text-lg font-light text-white mb-3">Quantity</h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center bg-white/10 backdrop-blur-sm border border-white/30 rounded">
                      <button
                        onClick={() => handleQuantityChange(-1)}
                        className="p-2 text-white/80 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="px-4 py-2 text-white font-light min-w-[3rem] text-center">
                        {quantity}
                      </span>
                      <button
                        onClick={() => handleQuantityChange(1)}
                        className="p-2 text-white/80 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                    <span className="text-white/60 text-sm font-light">
                      {product.inStock ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-4">
                  <Button
                    className="w-full bg-white text-black hover:bg-gray-100 py-3 font-light tracking-wide text-lg"
                    disabled={!product.inStock}
                  >
                    Add to Cart
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm py-3 font-light tracking-wide"
                  >
                    <Heart className="w-4 h-4 mr-2" />
                    Add to Wishlist
                  </Button>
                </div>

                {/* Additional Info */}
                <div className="pt-6 space-y-4 border-t border-white/20">
                  <div className="text-sm font-light text-white/80">
                    <p className="mb-2">• Free shipping on orders over $500</p>
                    <p className="mb-2">• 30-day return policy</p>
                    <p className="mb-2">• Complimentary gift wrapping</p>
                    <p>• Expert customer service</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ProductDetail;
