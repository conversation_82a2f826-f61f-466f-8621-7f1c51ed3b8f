import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const Women: React.FC = () => {
  const womenCategories = [
    {
      title: "Handbags",
      description: "Iconic handbags, totes, and evening bags for every occasion",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Small Leather Goods",
      description: "Wallets, cardholders, and accessories in premium leather",
      imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Shoes",
      description: "Elegant footwear from sneakers to pumps, crafted with Italian leather",
      imageUrl: "https://images.unsplash.com/photo-1543163521-1bf539c55dd2?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Accessories",
      description: "Scarves, belts, sunglasses, and jewelry to complete your look",
      imageUrl: "https://images.unsplash.com/photo-1601924994987-69e26d50dc26?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Ready-to-Wear",
      description: "Contemporary clothing that embodies effortless sophistication",
      imageUrl: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Fashion Jewelry",
      description: "Statement pieces and delicate accessories to elevate any outfit",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="Women"
      description="Explore our complete women's collection featuring handbags, shoes, accessories, and ready-to-wear pieces that celebrate feminine elegance and modern sophistication."
      backgroundImage="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=2000&auto=format&fit=crop"
      categories={womenCategories}
      layout="image-grid"
    />
  );
};

export default Women;
