import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Women: React.FC = () => {
  const womenCategories = [
    {
      title: "Handbags",
      description: "Iconic handbags, totes, and evening bags for every occasion",
      href: "/products?category=Women&subcategory=Handbags"
    },
    {
      title: "Small Leather Goods",
      description: "Wallets, cardholders, and accessories in premium leather",
      href: "/products?category=Women&subcategory=Wallets"
    },
    {
      title: "Shoes",
      description: "Elegant footwear from sneakers to pumps, crafted with Italian leather",
      href: "/products?category=Women&subcategory=Shoes"
    },
    {
      title: "Accessories",
      description: "Scarves, belts, sunglasses, and jewelry to complete your look",
      href: "/products?category=Women&subcategory=Accessories"
    },
    {
      title: "Ready-to-Wear",
      description: "Contemporary clothing that embodies effortless sophistication",
      href: "/products?category=Women&subcategory=Ready-to-Wear"
    },
    {
      title: "Fashion Jewelry",
      description: "Statement pieces and delicate accessories to elevate any outfit",
      href: "/jewelry"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  For Her
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Women's
                  <br />
                  Collection
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Explore our complete women's collection featuring handbags, shoes, accessories, and ready-to-wear pieces that celebrate feminine elegance and modern sophistication.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Women" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Women's
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Feminine Elegance
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {womenCategories.map((category) => (
              <Link
                key={category.title}
                to={category.href}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Collections */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white mb-4">
              Signature Pieces
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { title: "Iconic Bags", subtitle: "Handbags", imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop" },
              { title: "Fine Jewelry", subtitle: "Collections", imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop" },
              { title: "Designer Shoes", subtitle: "Footwear", imageUrl: "https://images.unsplash.com/photo-1543163521-1bf539c55dd2?q=80&w=800&auto=format&fit=crop" },
              { title: "Silk Scarves", subtitle: "Accessories", imageUrl: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?q=80&w=800&auto=format&fit=crop" }
            ].map((item) => (
              <Link
                key={item.title}
                to={`/products?category=Women`}
                className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg"
              >
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-between p-4 text-white">
                  <div className="text-right">
                    <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                      WOMEN
                    </span>
                  </div>
                  <div className="text-center">
                    <h3 className="font-serif text-lg font-light mb-1">{item.title}</h3>
                    <p className="text-xs text-white/80 mb-2">{item.subtitle}</p>
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Shop Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Women;
