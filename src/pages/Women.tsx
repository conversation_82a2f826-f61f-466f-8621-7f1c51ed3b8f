import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, User, ShoppingBag, Filter, Grid3X3, List, ChevronDown } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { AuthSheet } from "@/components/AuthDialog";
import { MainMenu } from "@/components/MainMenu";

const Women = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const products = [
    {
      id: 1,
      name: "Silk Evening Dress",
      price: "$1,250",
      originalPrice: "$1,500",
      category: "Ready-to-Wear",
      imageUrl: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?q=80&w=2088&auto=format&fit=crop",
      isNew: true,
      isSale: true
    },
    {
      id: 2,
      name: "Cashmere Sweater",
      price: "$850",
      category: "Knitwear",
      imageUrl: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?q=80&w=2305&auto=format&fit=crop",
      isNew: false,
      isSale: false
    },
    {
      id: 3,
      name: "Leather Handbag",
      price: "$2,100",
      category: "Handbags",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop",
      isNew: true,
      isSale: false
    },
    {
      id: 4,
      name: "Diamond Earrings",
      price: "$3,200",
      originalPrice: "$3,800",
      category: "Jewelry",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2370&auto=format&fit=crop",
      isNew: false,
      isSale: true
    },
    {
      id: 5,
      name: "Silk Scarf",
      price: "$285",
      category: "Accessories",
      imageUrl: "https://images.unsplash.com/photo-1511499767150-a48a237f0083?q=80&w=2080&auto=format&fit=crop",
      isNew: true,
      isSale: false
    },
    {
      id: 6,
      name: "High Heel Pumps",
      price: "$750",
      category: "Shoes",
      imageUrl: "https://images.unsplash.com/photo-1543163521-1bf539c55dd2?q=80&w=2080&auto=format&fit=crop",
      isNew: false,
      isSale: false
    }
  ];

  const backgroundImage = "https://images.unsplash.com/photo-1469334031218-e382a71b716b";

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start">
              <MainMenu />
            </div>
            <div className="text-center">
              <Link to="/" className="font-serif text-2xl font-light tracking-wider text-white hover:text-gray-200 transition-colors">
                LORIYA
              </Link>
            </div>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Wishlist</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your wishlist is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
              <AuthSheet>
                <button className="hover:text-gray-200 transition-colors">
                  <User size={20} />
                </button>
              </AuthSheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                      0
                    </span>
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                      </div>
                      <div className="flex-grow flex items-center justify-center">
                        <p className="text-white/80">Your cart is empty.</p>
                      </div>
                      <div className="flex-shrink-0">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                          </div>
                      </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="px-8 py-16 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="font-serif text-4xl md:text-6xl font-light text-white mb-6">
              Women's Collection
            </h1>
            <p className="text-lg md:text-xl font-light text-white/90 mb-8 leading-relaxed">
              Discover our carefully curated collection of contemporary essentials, crafted with attention to detail and sustainable practices.
            </p>
          </div>
        </section>

        {/* Filter and View Controls */}
        <section className="px-8 pb-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
              <div className="flex items-center gap-4">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
                    <div className="h-full flex flex-col p-8 pt-20">
                      <div className="flex-shrink-0">
                        <h2 className="text-3xl font-light text-white mb-8">Filter</h2>
                      </div>
                      <div className="flex-grow space-y-6">
                        <div>
                          <h3 className="text-lg font-light text-white mb-3">Category</h3>
                          <div className="space-y-2">
                            {["Handbags", "Ready-to-Wear", "Shoes", "Accessories", "Jewelry", "Knitwear"].map((category) => (
                              <label key={category} className="flex items-center space-x-2">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{category}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-light text-white mb-3">Price Range</h3>
                          <div className="space-y-2">
                            {["Under $500", "$500 - $1,000", "$1,000 - $2,000", "Over $2,000"].map((range) => (
                              <label key={range} className="flex items-center space-x-2">
                                <input type="checkbox" className="rounded border-white/30 bg-white/10" />
                                <span className="text-white/80 font-light">{range}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <Separator className="mb-4 bg-white/20" />
                        <div className="space-y-1 text-sm font-light">
                          <p className="text-white/80">Can we help you?</p>
                          <p className="text-white tracking-wider">+1.866.LORIYA</p>
                        </div>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm">
                      Women
                      <ChevronDown className="w-4 h-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-white/10 backdrop-blur-sm border-white/30 text-white min-w-[200px]">
                    <DropdownMenuItem asChild>
                      <Link to="#" className="w-full text-white/80 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                        Handbags
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="#" className="w-full text-white/80 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                        Ready-to-Wear
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="#" className="w-full text-white/80 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                        Shoes
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="#" className="w-full text-white/80 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                        Accessories
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="#" className="w-full text-white/80 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                        Jewelry
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <p className="text-white/80 font-light">{products.length} items</p>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' 
                    ? 'bg-white text-black hover:bg-gray-100' 
                    : 'bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm'
                  }
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' 
                    ? 'bg-white text-black hover:bg-gray-100' 
                    : 'bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm'
                  }
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Products Grid */}
        <section className="px-8 pb-16">
          <div className="max-w-6xl mx-auto">
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6'}>
              {products.map((product) => (
                <Link key={product.id} to={`/product/${product.id}`} className={viewMode === 'grid' ? 'group block' : 'flex gap-6 bg-white/5 backdrop-blur-sm rounded-lg p-6 hover:bg-white/10 transition-colors'}>
                  <div className={viewMode === 'grid' ? 'relative aspect-[3/4] overflow-hidden rounded-lg mb-4' : 'relative w-48 h-48 flex-shrink-0 overflow-hidden rounded-lg'}>
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                    {product.isNew && (
                      <span className="absolute top-4 left-4 bg-white text-black px-2 py-1 text-xs font-light uppercase tracking-wider">
                        New
                      </span>
                    )}
                    {product.isSale && (
                      <span className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 text-xs font-light uppercase tracking-wider">
                        Sale
                      </span>
                    )}
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        // Add to wishlist logic here
                      }}
                      className="absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Heart className="w-5 h-5 text-white" />
                    </button>
                  </div>

                  <div className={viewMode === 'grid' ? 'text-center' : 'flex-1 flex flex-col justify-center'}>
                    <p className="text-xs font-light tracking-wider text-white/60 uppercase mb-2">{product.category}</p>
                    <h3 className="text-lg font-light text-white mb-2 group-hover:text-gray-200 transition-colors">{product.name}</h3>
                    <div className="flex items-center gap-2 justify-center">
                      <span className="text-white font-light">{product.price}</span>
                      {product.originalPrice && (
                        <span className="text-white/60 line-through text-sm">{product.originalPrice}</span>
                      )}
                    </div>
                    {viewMode === 'list' && (
                      <Button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          // Add to cart logic here
                        }}
                        className="mt-4 bg-white text-black hover:bg-gray-100 font-light tracking-wide w-fit"
                      >
                        Add to Cart
                      </Button>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Women;
