import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const Jewelry: React.FC = () => {
  const jewelryCategories = [
    {
      title: "Fine Jewelry Collections",
      description: "Exquisite collections featuring diamonds, precious stones, and gold",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Necklaces & Pendants",
      description: "Elegant necklaces from delicate chains to statement pieces",
      imageUrl: "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Bracelets",
      description: "Sophisticated bracelets in gold, silver, and precious stones",
      imageUrl: "https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Earrings",
      description: "From subtle studs to dramatic drops, find your perfect pair",
      imageUrl: "https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Rings",
      description: "Cocktail rings, bands, and statement pieces for every occasion",
      imageUrl: "https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Engagement & Wedding",
      description: "Celebrate your love with our bridal jewelry collection",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Jewelry",
      description: "Sophisticated pieces designed for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "LORIYA Diamonds",
      description: "Our signature diamond collection featuring exceptional stones",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="Jewelry"
      description="Discover our exquisite jewelry collections featuring diamonds, precious stones, and fine metals. Each piece is crafted with exceptional artistry and attention to detail."
      backgroundImage="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2000&auto=format&fit=crop"
      categories={jewelryCategories}
      layout="image-grid"
    />
  );
};

export default Jewelry;
