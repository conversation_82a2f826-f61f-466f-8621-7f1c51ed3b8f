import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Jewelry: React.FC = () => {
  const jewelryCategories = [
    {
      title: "Fine Jewelry Collections",
      description: "Exquisite collections featuring diamonds, precious stones, and gold",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Necklaces & Pendants",
      description: "Elegant necklaces from delicate chains to statement pieces",
      imageUrl: "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Bracelets",
      description: "Sophisticated bracelets in gold, silver, and precious stones",
      imageUrl: "https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Earrings",
      description: "From subtle studs to dramatic drops, find your perfect pair",
      imageUrl: "https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Rings",
      description: "Cocktail rings, bands, and statement pieces for every occasion",
      imageUrl: "https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Engagement & Wedding",
      description: "Celebrate your love with our bridal jewelry collection",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Jewelry",
      description: "Sophisticated pieces designed for the modern gentleman",
      imageUrl: "https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "LORIYA Diamonds",
      description: "Our signature diamond collection featuring exceptional stones",
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Fine Jewelry
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Jewelry
                  <br />
                  Collections
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our exquisite jewelry collections featuring diamonds, precious stones, and fine metals. Each piece is crafted with exceptional artistry and attention to detail.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Jewelry" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Jewelry
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Timeless Elegance
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {jewelryCategories.map((category) => (
              <Link
                key={category.title}
                to={`/products?category=Jewelry&subcategory=${encodeURIComponent(category.title)}`}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Jewelry;
