import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { Grid, List, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ProductGrid } from '@/components/products/ProductGrid';
import { ProductFilters } from '@/components/products/ProductFilters';
import { useProducts, sortOptions } from '@/hooks/useProducts';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Products: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const category = searchParams.get('category') || undefined;
  const subcategory = searchParams.get('subcategory') || undefined;

  const {
    products,
    filters,
    sortBy,
    updateFilters,
    clearFilters,
    setSortBy,
    totalProducts
  } = useProducts({ category, subcategory });

  const handleFiltersChange = (newFilters: any) => {
    updateFilters(newFilters);
    
    // Update URL params
    const params = new URLSearchParams(searchParams);
    if (newFilters.category) {
      params.set('category', newFilters.category);
    } else if (newFilters.category === undefined) {
      params.delete('category');
    }
    
    if (newFilters.subcategory) {
      params.set('subcategory', newFilters.subcategory);
    } else if (newFilters.subcategory === undefined) {
      params.delete('subcategory');
    }
    
    setSearchParams(params);
  };

  const handleClearFilters = () => {
    clearFilters();
    setSearchParams({});
  };

  const getPageTitle = () => {
    if (subcategory) return subcategory;
    if (category) return category;
    return 'All Products';
  };

  const getBreadcrumb = () => {
    const parts = ['Products'];
    if (category) parts.push(category);
    if (subcategory) parts.push(subcategory);
    return parts.join(' / ');
  };

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <nav className="text-sm font-light tracking-widest text-white/60 uppercase">
                  {getBreadcrumb()}
                </nav>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  {getPageTitle()}
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our curated collection of luxury items, crafted with exceptional attention to detail and timeless elegance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="px-8 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-64 flex-shrink-0">
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <ProductFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={handleClearFilters}
                />
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              {/* Toolbar */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-light text-white/80">
                    {totalProducts} {totalProducts === 1 ? 'product' : 'products'}
                  </span>
                  {(filters.category || filters.subcategory) && (
                    <>
                      <Separator orientation="vertical" className="h-4 bg-white/20" />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClearFilters}
                        className="text-sm font-light text-white/80 hover:text-white"
                      >
                        Clear filters
                      </Button>
                    </>
                  )}
                </div>

                <div className="flex items-center gap-4">
                  {/* Sort */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-light text-white/80 hidden sm:block">Sort by:</span>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-48 font-light bg-white/10 border-white/30 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {sortOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value} className="font-light">
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* View Toggle (Future Enhancement) */}
                  <div className="hidden sm:flex items-center border border-white/20 rounded-md">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-white/10 text-white hover:bg-white hover:text-black"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-white/60 hover:bg-white hover:text-black"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Products Grid */}
              <ProductGrid
                products={products}
                columns={{ mobile: 2, tablet: 3, desktop: 3 }}
              />

              {/* Load More (Future Enhancement) */}
              {products.length > 0 && (
                <div className="text-center mt-12">
                  <Button
                    variant="outline"
                    className="font-light tracking-wider px-8 border-white text-white hover:bg-white hover:text-black"
                    disabled
                  >
                    Load More Products
                  </Button>
                  <p className="text-sm text-white/60 mt-2 font-light">
                    Showing all {totalProducts} products
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Products;
