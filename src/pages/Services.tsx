import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const Services: React.FC = () => {
  const serviceCategories = [
    {
      title: "Personalization Services",
      description: "Make your LORIYA pieces uniquely yours with our monogramming and engraving services",
      imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Mon Monogram",
      description: "Classic monogramming service for bags and leather goods",
      imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "LORIYA Repairs",
      description: "Expert restoration and repair services for your cherished pieces",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Personal Shopping",
      description: "One-on-one styling sessions with our expert personal shoppers",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "VIP Services",
      description: "Exclusive services for our most valued clients",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Gift Services",
      description: "Professional gift wrapping and delivery services",
      imageUrl: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Appointment Booking",
      description: "Schedule a private consultation at your preferred boutique",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Care Instructions",
      description: "Learn how to properly care for your LORIYA pieces",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Exceptional Service
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Our
                  <br />
                  Services
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Experience the exceptional service that defines LORIYA. From personalization to repairs, our expert team is dedicated to ensuring your complete satisfaction with every piece.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Explore Services
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Offerings
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Dedicated to Excellence
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {serviceCategories.map((service) => (
              <div
                key={service.title}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {service.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Learn More
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default Services;
