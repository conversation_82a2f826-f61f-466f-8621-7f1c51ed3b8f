import React from 'react';
import { Link } from 'react-router-dom';
import { LoriyaLayout } from '@/components/layout/LoriyaLayout';

const BagsAndWallets: React.FC = () => {
  const bagCategories = [
    {
      title: "Women's Handbags",
      description: "Iconic handbags, totes, and shoulder bags crafted with exceptional attention to detail",
      href: "/products?category=Bags and Wallets&subcategory=Women's Bags"
    },
    {
      title: "Men's Bags",
      description: "Sophisticated briefcases, messenger bags, and travel companions for the modern man",
      href: "/products?category=Bags and Wallets&subcategory=Men's Bags"
    },
    {
      title: "Women's Small Leather Goods",
      description: "Wallets, cardholders, and accessories in premium leather",
      href: "/products?category=Bags and Wallets&subcategory=Women's Small Leather Goods"
    },
    {
      title: "Men's Small Leather Goods",
      description: "Wallets, money clips, and leather accessories for everyday elegance",
      href: "/products?category=Bags and Wallets&subcategory=Men's Small Leather Goods"
    },
    {
      title: "Iconic Collections",
      description: "Our signature bag designs that have become timeless classics",
      href: "/products?category=Bags and Wallets&isFeatured=true"
    },
    {
      title: "Monogram Signature",
      description: "Personalized pieces featuring our distinctive monogram pattern",
      href: "/products?category=Bags and Wallets&subcategory=Personalization"
    }
  ];

  return (
    <LoriyaLayout backgroundImage="https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2000&auto=format&fit=crop">
      {/* Hero Section */}
      <section className="px-8 py-16 md:py-24">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Luxury Leather Goods
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Bags &
                  <br />
                  Wallets
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our complete collection of luxury handbags, leather goods, and accessories. Each piece combines traditional craftsmanship with contemporary design.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Link to="/products?category=Bags and Wallets" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                  Shop Collection
                  <span className="block w-full h-px bg-white mt-1"></span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-sm font-light tracking-widest text-white/70 uppercase">
              Our Collections
            </p>
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
              Crafted to Perfection
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {bagCategories.map((category) => (
              <Link
                key={category.title}
                to={category.href}
                className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-white/10 backdrop-blur-sm border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                  <h3 className="text-lg font-light mb-2 group-hover:text-gray-200 transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/80 font-light mb-4">
                    {category.description}
                  </p>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Explore Collection
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl md:text-4xl font-light text-white mb-4">
              Iconic Designs
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { title: "Iconic Handbags", imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop" },
              { title: "Men's Briefcases", imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=800&auto=format&fit=crop" },
              { title: "Wallets", imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop" },
              { title: "Monogram", imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=800&auto=format&fit=crop" }
            ].map((item) => (
              <Link
                key={item.title}
                to={`/products?category=Bags and Wallets`}
                className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg"
              >
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute inset-0 flex flex-col justify-between p-4 text-white">
                  <div className="text-right">
                    <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                      LUXURY
                    </span>
                  </div>
                  <div className="text-center">
                    <h3 className="font-serif text-lg font-light mb-2">{item.title}</h3>
                    <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                      Shop Now
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </LoriyaLayout>
  );
};

export default BagsAndWallets;
