import React from 'react';
import { CategoryPage } from '@/components/layout/CategoryPage';

const BagsAndWallets: React.FC = () => {
  const bagCategories = [
    {
      title: "Women's Handbags",
      description: "Iconic handbags, totes, and shoulder bags crafted with exceptional attention to detail",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Bags",
      description: "Sophisticated briefcases, messenger bags, and travel companions for the modern man",
      imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Women's Small Leather Goods",
      description: "Wallets, cardholders, and accessories in premium leather",
      imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Men's Small Leather Goods",
      description: "Wallets, money clips, and leather accessories for everyday elegance",
      imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Iconic Collections",
      description: "Our signature bag designs that have become timeless classics",
      imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=800&auto=format&fit=crop"
    },
    {
      title: "Monogram Signature",
      description: "Personalized pieces featuring our distinctive monogram pattern",
      imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=800&auto=format&fit=crop"
    }
  ];

  return (
    <CategoryPage
      title="Bags & Wallets"
      description="Discover our complete collection of luxury handbags, leather goods, and accessories. Each piece combines traditional craftsmanship with contemporary design."
      backgroundImage="https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2000&auto=format&fit=crop"
      categories={bagCategories}
      layout="image-grid"
    />
  );
};

export default BagsAndWallets;
