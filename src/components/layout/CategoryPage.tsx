import React from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { ArrowLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface CategoryItem {
  title: string;
  description?: string;
  imageUrl?: string;
  subcategories?: CategoryItem[];
  href?: string;
}

interface CategoryPageProps {
  title: string;
  description?: string;
  backgroundImage?: string;
  categories: CategoryItem[];
  layout?: 'grid' | 'list' | 'image-grid';
  showBackButton?: boolean;
}

export const CategoryPage: React.FC<CategoryPageProps> = ({
  title,
  description,
  backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721",
  categories,
  layout = 'grid',
  showBackButton = false
}) => {
  const [searchParams] = useSearchParams();
  const subcategory = searchParams.get('subcategory');

  const getBreadcrumb = () => {
    const parts = [title];
    if (subcategory) parts.push(subcategory);
    return parts.join(' / ');
  };

  const renderCategoryItem = (item: CategoryItem, index: number) => {
    const href = item.href || `/products?category=${encodeURIComponent(title)}&subcategory=${encodeURIComponent(item.title)}`;
    
    if (layout === 'image-grid') {
      return (
        <Link
          key={item.title}
          to={href}
          className="group relative aspect-[4/5] overflow-hidden rounded-lg bg-gray-100"
        >
          <img
            src={item.imageUrl || `https://images.unsplash.com/photo-${1500000000000 + index}?q=80&w=800&auto=format&fit=crop`}
            alt={item.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
            <h3 className="text-lg font-light mb-2">{item.title}</h3>
            {item.description && (
              <p className="text-sm text-white/80 font-light">{item.description}</p>
            )}
            <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 group-hover:bg-white group-hover:text-black">
                Explore Collection
              </span>
            </div>
          </div>
        </Link>
      );
    }

    if (layout === 'list') {
      return (
        <Link
          key={item.title}
          to={href}
          className="group flex items-center justify-between p-6 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
        >
          <div>
            <h3 className="text-lg font-light text-gray-900 group-hover:text-gray-600 transition-colors">
              {item.title}
            </h3>
            {item.description && (
              <p className="text-sm text-gray-600 font-light mt-1">{item.description}</p>
            )}
          </div>
          <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
        </Link>
      );
    }

    // Default grid layout
    return (
      <Link
        key={item.title}
        to={href}
        className="group relative aspect-square overflow-hidden rounded-lg bg-gray-100"
      >
        <img
          src={item.imageUrl || `https://images.unsplash.com/photo-${1500000000000 + index}?q=80&w=600&auto=format&fit=crop`}
          alt={item.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute inset-0 flex flex-col justify-end p-4 text-white">
          <h3 className="text-sm font-light uppercase tracking-wider">{item.title}</h3>
          <div className="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <span className="text-xs font-light tracking-wider">
              Shop Now →
            </span>
          </div>
        </div>
      </Link>
    );
  };

  const getGridClasses = () => {
    switch (layout) {
      case 'image-grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8';
      case 'list':
        return 'space-y-4';
      default:
        return 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div 
        className="relative bg-cover bg-center bg-gray-900"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      >
        <div className="absolute inset-0 bg-black/50" />
        <div className="relative z-10 container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center text-white">
            {showBackButton && (
              <div className="flex justify-start mb-8">
                <Button
                  variant="ghost"
                  onClick={() => window.history.back()}
                  className="text-white hover:text-gray-200 font-light"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </div>
            )}
            
            <nav className="text-sm font-light tracking-widest text-white/60 mb-4">
              {getBreadcrumb()}
            </nav>
            
            <h1 className="text-4xl lg:text-5xl font-light tracking-wide mb-4">
              {title}
            </h1>
            
            {description && (
              <p className="text-lg font-light text-white/80 max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-12">
        <div className={getGridClasses()}>
          {categories.map((item, index) => renderCategoryItem(item, index))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <Separator className="mb-8" />
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-light text-gray-900 mb-4">
              Need Help Finding Something?
            </h2>
            <p className="text-gray-600 font-light mb-6">
              Our personal shopping experts are here to help you discover the perfect pieces for your style.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="font-light tracking-wider">
                Book an Appointment
              </Button>
              <Button className="bg-black text-white hover:bg-gray-800 font-light tracking-wider">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
