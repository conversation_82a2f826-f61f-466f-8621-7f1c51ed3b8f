import React, { useState } from "react";
import { Link } from "react-router-dom";
import { Heart, User, ShoppingBag, Menu, ChevronRight, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useCart } from "@/context/CartContext";
import { toast } from "sonner";

interface MenuItem {
  title: string;
  subcategories?: MenuItem[];
  layout?: string;
}

interface LoriyaLayoutProps {
  children: React.ReactNode;
  backgroundImage?: string;
}

export const LoriyaLayout: React.FC<LoriyaLayoutProps> = ({ 
  children, 
  backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721" 
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [contactName, setContactName] = useState("");
  const [contactEmail, setContactEmail] = useState("");
  const [contactMessage, setContactMessage] = useState("");
  const { state: cartState } = useCart();

  const menuItems: MenuItem[] = [
    {
      title: "Gifts",
      subcategories: [
        { title: "Gifts for Her" },
        { title: "Gifts for Home" },
        { title: "Gifts for Babies" },
        { title: "Personalization" },
      ],
    },
    {
      title: "New",
      layout: "image-grid-variant",
      subcategories: [
        {
          title: "For Women",
          layout: "image-grid-variant",
          subcategories: [
            { title: "New Arrivals" },
            { title: "Pre-Fall 2025 Collection" },
            { title: "Spring 2025 Collection" },
            { title: "New Formal Collection" },
            { title: "Spring-Summer 2025 Show" },
          ],
        },
        {
          title: "For Men",
          layout: "image-grid-variant",
          subcategories: [
            { title: "New Arrivals" },
            { title: "Pre-Fall 2025 Collection" },
            { title: "Spring 2025 Collection" },
            { title: "New Formal Collection" },
            { title: "Spring-Summer 2025 Show" },
          ],
        },
      ],
    },
    {
      title: "Bags and Wallets",
      subcategories: [
        {
          title: "Women's Bags",
          subcategories: [
            { title: "Iconic Handbags" },
            { title: "All Handbags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "Capucines" },
          ],
        },
        {
          title: "Men's Bags",
          subcategories: [
            { title: "Iconic Bags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "All Bags" },
          ],
        },
        { title: "Women's Small Leather Goods" },
        { title: "Men's Small Leather Goods" },
        { title: "Personalization" },
      ],
    },
    {
      title: "Women",
      subcategories: [
        {
          title: "Handbags",
          layout: 'image-grid',
          subcategories: [
            { title: "Iconic Handbags" },
            { title: "All Handbags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "Capucines" },
          ],
        },
        {
          title: "Wallets",
          layout: 'image-grid',
          subcategories: [
            { title: "All Small Leather Goods" },
            { title: "New In" },
            { title: "Wallets on Chain and Nano Bags" },
            { title: "Card and Key Holders" },
            { title: "Compact and Long Wallets" },
            { title: "LORIYA Essentials" },
          ],
        },
        {
          title: "Shoes",
          layout: 'image-grid',
          subcategories: [
            { title: "All Shoes" },
            { title: "Sneakers" },
            { title: "Boots & Booties" },
            { title: "Loafers and Ballerinas" },
            { title: "Mules and Slides" },
            { title: "Sandals and Espadrilles" },
            { title: "Pumps" },
          ],
        },
        { 
          title: "Accessories",
          layout: 'image-grid',
          subcategories: [
            { title: "Key Holders & Bag Charms" },
            { title: "Sunglasses" },
            { title: "Belts" },
            { title: "Silk Squares and Bandeaus" },
            { title: "Scarves" },
            { title: "Hats and Gloves" },
            { title: "Hair Accessories" },
            { title: "Home Accessories" },
            { title: "Tech Accessories" },
          ]
        },
      ],
    },
    {
      title: "Men",
      subcategories: [
        {
          title: "Bags",
          layout: 'image-grid',
          subcategories: [
            { title: "Iconic Bags" },
            { title: "New In" },
            { title: "Monogram Signature" },
            { title: "All Bags" },
          ],
        },
        {
          title: "Wallets",
          layout: 'image-grid',
          subcategories: [
            { title: "All Wallets" },
            { title: "New In" },
            { title: "Compact and Long Wallets" },
            { title: "Card Holders" },
            { title: "Pouches" },
            { title: "Mini Bags" },
          ]
        },
        { title: "Shoes",
          layout: 'image-grid',
          subcategories: [
            { title: "All Shoes" },
            { title: "Sneakers" },
            { title: "Loafers and Moccasins" },
            { title: "Buckles and Lace-Ups" },
            { title: "Boots" },
            { title: "Sandals" },
          ],
        },
        { title: "Accessories",
          layout: 'image-grid',
          subcategories: [
            { title: "Belts" },
            { title: "Sunglasses" },
            { title: "Key Holders, Key Chains and Bag Charms" },
            { title: "Hats, Beanies and Gloves" },
            { title: "Scarves" },
            { title: "Ties and Pocket Squares" },
            { title: "Silk and Bandanas" },
            { title: "Home Textile" },
            { title: "Tech Accessories" },
          ]
        },
      ],
    },
    {
      title: "Jewelry",
      subcategories: [
        {
          title: "Collections",
          layout: 'image-grid',
          subcategories: [
            { title: "Color Blossom" },
            { title: "Idylle Blossom" },
            { title: "Le Damier de LORIYA" },
            { title: "Empreinte Collection" },
            { title: "Les Gastons LORIYA" },
            { title: "LORIYA Diamonds" },
            { title: "Silver Lockit" },
            { title: "Other Jewelry Collections" },
          ],
        },
        {
          title: "Categories",
          layout: 'image-grid',
          subcategories: [
            { title: "All Fine Jewelry" },
            { title: "Bracelets" },
            { title: "Earrings" },
            { title: "Necklaces and Pendants" },
            { title: "Rings" },
            { title: "Engagement Rings and Wedding Bands" },
            { title: "Men's Jewelry" },
          ],
        },
      ],
    },
    {
      title: "Watches",
      subcategories: [
        { title: "All Watches" },
        {
          title: "Watch Collections",
          layout: 'image-grid',
          subcategories: [
            { title: "Escale" },
            { title: "Tambour" },
            { title: "Tambour Convergence" },
            { title: "Tambour Taiko" },
            { title: "Original Tambour" },
            { title: "Objects of Time" },
          ],
        },
      ],
    },
    {
      title: "Perfumes",
      subcategories: [
        { 
          title: "Iconic Scents",
          layout: 'image-grid',
          subcategories: [
            { title: "Imagination" },
            { title: "Attrape-Rêves" },
            { title: "Spell On You" },
            { title: "L'Immensité" },
            { title: "Ombre Nomade" },
          ]
        },
        { 
          title: "Categories",
          layout: 'image-grid',
          subcategories: [
            { title: "All Perfumes" },
            { title: "Women's Perfumes" },
            { title: "Men's Perfumes" },
            { title: "Cologne Perfumes" },
            { title: "Oud Perfumes" },
          ]
        },
      ]
    },
    { 
      title: "Travel and Home",
      subcategories: [
        {
          title: "Travel Bags and Rolling Luggage",
          layout: 'image-grid',
          subcategories: [
            { title: "Rolling Luggage" },
            { title: "Travel Bags" },
            { title: "Travel Accessories" },
            { title: "Personalization" },
          ],
        },
        {
          title: "Home and Art of Dining",
          layout: 'image-grid',
          subcategories: [
            { title: "Furniture" },
            { title: "Decoration" },
            { title: "Art of Dining" },
            { title: "Home Textiles" },
          ]
        },
      ]
    },
    { 
      title: "Services",
      subcategories: [
        { 
          title: "Personalization",
          layout: 'image-grid',
          subcategories: [
            { title: "Mon Monogram" },
            { title: "LORIYA & I Alma Personalization" },
            { title: "Bags and Small Leather Goods" },
            { title: "Travel" },
            { title: "Jewelry" },
            { title: "Perfumes" }
          ]
        },
        { title: "LORIYA Repairs" }
      ]
    },
  ];

  const [menuStack, setMenuStack] = useState<{ items: MenuItem[]; title: string; layout?: string; }[]>(() => [{ items: menuItems, title: "Menu" }]);

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Contact form submitted:", { name: contactName, email: contactEmail, message: contactMessage });
    toast.success("Your message has been sent!");
    setContactName("");
    setContactEmail("");
    setContactMessage("");
  };

  const handleSheetOpenChange = (open: boolean) => {
    setIsMenuOpen(open);
    if (!open) {
      setTimeout(() => setMenuStack([{ items: menuItems, title: "Menu" }]), 300);
    }
  };

  const activeMenu = menuStack[menuStack.length - 1];

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
          <div className="grid grid-cols-3 items-center">
            <div className="flex justify-start">
              <Sheet open={isMenuOpen} onOpenChange={handleSheetOpenChange}>
                <SheetTrigger asChild>
                  <button
                    className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"
                  >
                    <Menu size={20} />
                    <span className="text-sm font-light tracking-wide">MENU</span>
                  </button>
                </SheetTrigger>
                <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-sm text-white p-0">
                  <div className="relative h-full overflow-x-hidden">
                    {/* Main Menu */}
                    <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "-translate-x-full" : "translate-x-0"}`}>
                      <div className="flex flex-col h-full pt-24">
                        <div className="flex-grow space-y-4 pb-8 overflow-y-auto px-8">
                          {menuItems.map((item) => {
                            const getItemHref = (title: string) => {
                              switch (title) {
                                case "Gifts": return "/gifts";
                                case "New": return "/new";
                                case "Bags and Wallets": return "/bags-and-wallets";
                                case "Women": return "/women";
                                case "Men": return "/men";
                                case "Jewelry": return "/jewelry";
                                case "Watches": return "/watches";
                                case "Perfumes": return "/perfumes";
                                case "Travel and Home": return "/travel-and-home";
                                case "Services": return "/services";
                                default: return "#";
                              }
                            };

                            return (
                              <div key={item.title}>
                                {item.subcategories ? (
                                  <a
                                    href="#"
                                    className="flex items-center justify-between w-full text-2xl font-light text-white hover:text-gray-300 transition-colors"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setMenuStack(stack => [...stack, {items: item.subcategories, title: item.title, layout: item.layout}]);
                                    }}
                                  >
                                    <span>{item.title}</span>
                                    <ChevronRight className="w-5 h-5" />
                                  </a>
                                ) : (
                                  <Link
                                    to={getItemHref(item.title)}
                                    className="flex items-center justify-between w-full text-2xl font-light text-white hover:text-gray-300 transition-colors"
                                    onClick={() => setIsMenuOpen(false)}
                                  >
                                    <span>{item.title}</span>
                                  </Link>
                                )}
                              </div>
                            );
                          })}
                          <div>
                            <div className="text-2xl font-light text-white">The Maison LORIYA</div>
                          </div>
                        </div>
                        <div className="flex-shrink-0 pt-4 px-8 pb-12">
                          <Separator className="mb-4 bg-white/20" />
                          <div className="space-y-1 text-sm font-light">
                            <p className="text-white/80">Can we help you?</p>
                            <p className="text-white tracking-wider">+1.866.LORIYA</p>
                            <div className="mt-4 space-y-2 text-sm font-light">
                              <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                                ADDRESS <span>→</span>
                              </a>
                              <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                                OPENING HOURS <span>→</span>
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Sub Menu Container */}
                    <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "translate-x-0" : "translate-x-full"}`}>
                      {menuStack.length > 1 && (
                        <div className="flex flex-col h-full">
                          {/* Header */}
                          <div className="pt-24 pb-6 px-8 flex-shrink-0">
                            <button
                              onClick={() => setMenuStack(stack => stack.slice(0, -1))}
                              className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider text-sm font-light"
                            >
                              <ArrowLeft className="w-4 h-4" />
                              <span>Back</span>
                            </button>

                            <h2 className="text-3xl font-light text-white mt-8 mb-6">{activeMenu.title}</h2>
                          </div>

                          {/* Scrollable Content + Footer */}
                          <div className="overflow-y-auto px-8 pb-12 flex-grow">
                            {/* Content */}
                            <div>
                              {activeMenu.layout === 'image-grid' ? (
                                <div>
                                  <div className="mb-8">
                                    <p className="text-sm font-light tracking-widest text-white/80 uppercase mb-4">New Arrivals</p>
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <a href="#" className="group">
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <p className="text-xs font-light text-white/80 group-hover:text-white">Pre-Fall 2025 Collection</p>
                                        </a>
                                      </div>
                                      <div>
                                        <a href="#" className="group">
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <p className="text-xs font-light text-white/80 group-hover:text-white">Spring 2025 Collection</p>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                                    {activeMenu.items.map((subItem) => {
                                      const getSubItemHref = (parentTitle: string, subTitle: string) => {
                                        const categoryParam = encodeURIComponent(parentTitle);
                                        const subcategoryParam = encodeURIComponent(subTitle);
                                        return `/products?category=${categoryParam}&subcategory=${subcategoryParam}`;
                                      };

                                      return subItem.subcategories ? (
                                        <a
                                          key={subItem.title}
                                          href="#"
                                          className="group"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                          }}
                                        >
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                        </a>
                                      ) : (
                                        <Link
                                          key={subItem.title}
                                          to={getSubItemHref(activeMenu.title, subItem.title)}
                                          className="group"
                                          onClick={() => setIsMenuOpen(false)}
                                        >
                                          <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                            <p className="text-white/40 text-xs">Image</p>
                                          </div>
                                          <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                        </Link>
                                      );
                                    })}
                                  </div>
                                </div>
                              ) : activeMenu.layout === 'image-grid-variant' ? (
                                <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                                  {activeMenu.items.map((subItem) => {
                                    const getSubItemHref = (parentTitle: string, subTitle: string) => {
                                      const categoryParam = encodeURIComponent(parentTitle);
                                      const subcategoryParam = encodeURIComponent(subTitle);
                                      return `/products?category=${categoryParam}&subcategory=${subcategoryParam}`;
                                    };

                                    return subItem.subcategories ? (
                                      <a
                                        key={subItem.title}
                                        href="#"
                                        className="group"
                                        onClick={(e) => {
                                          e.preventDefault();
                                          setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                        }}
                                      >
                                        <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                          <p className="text-white/40 text-xs">Image</p>
                                        </div>
                                        <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                      </a>
                                    ) : (
                                      <Link
                                        key={subItem.title}
                                        to={getSubItemHref(activeMenu.title, subItem.title)}
                                        className="group"
                                        onClick={() => setIsMenuOpen(false)}
                                      >
                                        <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                          <p className="text-white/40 text-xs">Image</p>
                                        </div>
                                        <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                                      </Link>
                                    );
                                  })}
                                </div>
                              ) : (
                                <div className="space-y-3">
                                  {activeMenu.items.map((subItem) => {
                                    const getSubItemHref = (parentTitle: string, subTitle: string) => {
                                      const categoryParam = encodeURIComponent(parentTitle);
                                      const subcategoryParam = encodeURIComponent(subTitle);
                                      return `/products?category=${categoryParam}&subcategory=${subcategoryParam}`;
                                    };

                                    return subItem.subcategories ? (
                                      <a
                                        key={subItem.title}
                                        href="#"
                                        className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                                        onClick={(e) => {
                                          e.preventDefault();
                                          setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                        }}
                                      >
                                        <span>{subItem.title}</span>
                                        <ChevronRight className="w-5 h-5" />
                                      </a>
                                    ) : (
                                      <Link
                                        key={subItem.title}
                                        to={getSubItemHref(activeMenu.title, subItem.title)}
                                        className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                                        onClick={() => setIsMenuOpen(false)}
                                      >
                                        <span>{subItem.title}</span>
                                      </Link>
                                    );
                                  })}
                                </div>
                              )}
                            </div>

                            {/* Footer */}
                            <div className="pt-4">
                              <Separator className="mb-4 bg-white/20" />
                              <div className="space-y-1 text-sm font-light">
                                <p className="text-white/80">Can we help you?</p>
                                <p className="text-white tracking-wider">+1.866.LORIYA</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            <Link to="/" className="text-center font-serif text-2xl font-light tracking-wider text-white">
              LORIYA
            </Link>
            <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
              <Sheet>
                <SheetTrigger asChild>
                  <button className="text-sm font-light tracking-wide hover:text-gray-200 transition-colors hidden md:block">
                    Contact Us
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                    <div className="flex-shrink-0">
                      <h2 className="text-3xl font-light text-white">Contact Us</h2>
                    </div>
                    <div className="flex-grow py-8 overflow-y-auto">
                      <form onSubmit={handleContactSubmit} className="space-y-6">
                        <div>
                          <label htmlFor="contact-name" className="block text-sm font-light text-white/80 mb-2">Name</label>
                          <Input 
                            id="contact-name"
                            type="text"
                            placeholder="Your name"
                            value={contactName}
                            onChange={(e) => setContactName(e.target.value)}
                            className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="contact-email" className="block text-sm font-light text-white/80 mb-2">Email</label>
                          <Input
                            id="contact-email"
                            type="email"
                            placeholder="Your email"
                            value={contactEmail}
                            onChange={(e) => setContactEmail(e.target.value)}
                            className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="contact-message" className="block text-sm font-light text-white/80 mb-2">Message</label>
                          <Textarea
                            id="contact-message"
                            placeholder="How can we help you?"
                            value={contactMessage}
                            onChange={(e) => setContactMessage(e.target.value)}
                            className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm w-full min-h-[120px]"
                            required
                          />
                        </div>
                        <Button 
                          type="submit"
                          className="w-full bg-white text-black hover:bg-gray-100 px-8 py-3 font-light tracking-wide"
                        >
                          Send Message
                        </Button>
                      </form>
                    </div>
                    <div className="flex-shrink-0">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-1 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="hover:text-gray-200 transition-colors">
                    <Heart size={20} />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                    <div className="flex-shrink-0">
                      <h2 className="text-3xl font-light text-white">Wishlist</h2>
                    </div>
                    <div className="flex-grow flex items-center justify-center">
                      <p className="text-white/80">Your wishlist is empty.</p>
                    </div>
                    <div className="flex-shrink-0">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-1 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <button className="hover:text-gray-200 transition-colors">
                <User size={20} />
              </button>
              <Sheet>
                <SheetTrigger asChild>
                  <button className="relative hover:text-gray-200 transition-colors">
                    <ShoppingBag size={20} />
                    {cartState.itemCount > 0 && (
                      <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                        {cartState.itemCount}
                      </span>
                    )}
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-sm text-white p-0">
                  <div className="h-full flex flex-col p-8 pt-20">
                    <div className="flex-shrink-0">
                      <h2 className="text-3xl font-light text-white">Shopping Cart</h2>
                    </div>
                    <div className="flex-grow flex items-center justify-center">
                      {cartState.itemCount === 0 ? (
                        <div className="text-center">
                          <p className="text-white/80 mb-4">Your cart is empty.</p>
                          <Link to="/products">
                            <Button className="bg-white text-black hover:bg-gray-100 font-light tracking-wider">
                              Browse Products
                            </Button>
                          </Link>
                        </div>
                      ) : (
                        <div className="w-full">
                          <p className="text-white/80 text-center">
                            {cartState.itemCount} {cartState.itemCount === 1 ? 'item' : 'items'} in cart
                          </p>
                          <p className="text-white text-center text-lg font-light mt-2">
                            Total: ${cartState.total.toFixed(2)}
                          </p>
                          <div className="mt-6 space-y-3">
                            <Link to="/products" className="block">
                              <Button className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wider">
                                View Cart
                              </Button>
                            </Link>
                            <Link to="/products" className="block">
                              <Button variant="outline" className="w-full border-white text-white hover:bg-white hover:text-black font-light tracking-wider">
                                Continue Shopping
                              </Button>
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-1 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>

        {/* Page Content */}
        {children}
      </div>
    </div>
  );
};
