import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Eye, EyeOff, ArrowLeft } from "lucide-react";
import { toast } from "sonner";

interface AuthSheetProps {
  children: React.ReactNode;
}

export function AuthSheet({ children }: AuthSheetProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentView, setCurrentView] = useState<"main" | "signin" | "signup">("main");

  // Sign In Form State
  const [signInData, setSignInData] = useState({
    email: "",
    password: "",
  });

  // Sign Up Form State
  const [signUpData, setSignUpData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const handleSignIn = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!signInData.email || !signInData.password) {
      toast.error("Please fill in all fields");
      return;
    }

    // Simulate sign in
    console.log("Sign in:", signInData);
    toast.success("Welcome back!");

    // Reset form and close
    setSignInData({ email: "", password: "" });
    setCurrentView("main");
  };

  const handleGoogleAuth = () => {
    // Simulate Google authentication
    console.log("Google authentication");
    toast.success("Signed in with Google!");
    setCurrentView("main");
  };

  const handleSignUp = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!signUpData.name || !signUpData.email || !signUpData.password || !signUpData.confirmPassword) {
      toast.error("Please fill in all fields");
      return;
    }

    if (signUpData.password !== signUpData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (signUpData.password.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    // Simulate sign up
    console.log("Sign up:", signUpData);
    toast.success("Account created successfully!");

    // Reset form and close
    setSignUpData({ name: "", email: "", password: "", confirmPassword: "" });
    setCurrentView("main");
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent side="right" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
        <div className="h-full flex flex-col p-8 pt-20">

          {/* Main Menu */}
          {currentView === "main" && (
            <div className="h-full flex flex-col">
              <div className="flex-shrink-0 mb-8">
                <h2 className="text-3xl font-light text-white mb-2">Welcome to Loriya</h2>
                <p className="text-white/80 text-sm">Sign in to your account or create a new one</p>
              </div>

              <div className="flex-grow space-y-4">
                {/* Google Sign In */}
                <Button
                  onClick={handleGoogleAuth}
                  className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wide flex items-center justify-center gap-3"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-white/20" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-transparent px-2 text-white/60">Or</span>
                  </div>
                </div>

                {/* Sign In Button */}
                <Button
                  onClick={() => setCurrentView("signin")}
                  className="w-full border border-white/30 bg-transparent text-white hover:bg-white/10 hover:text-white font-light tracking-wide"
                >
                  Sign In with Email
                </Button>

                {/* Sign Up Button */}
                <Button
                  onClick={() => setCurrentView("signup")}
                  className="w-full border border-white/30 bg-transparent text-white hover:bg-white/10 hover:text-white font-light tracking-wide"
                >
                  Create Account
                </Button>
              </div>

              <div className="flex-shrink-0 pt-8">
                <div className="text-center text-xs text-white/60">
                  By continuing, you agree to our Terms of Service and Privacy Policy.
                </div>
              </div>
            </div>
          )}

          {/* Sign In View */}
          {currentView === "signin" && (
            <div className="h-full flex flex-col">
              <div className="flex-shrink-0 mb-6">
                <button
                  onClick={() => setCurrentView("main")}
                  className="flex items-center text-white/80 hover:text-white transition-colors mb-4"
                >
                  <ArrowLeft size={20} className="mr-2" />
                  Back
                </button>
                <h2 className="text-3xl font-light text-white mb-2">Sign In</h2>
                <p className="text-white/80 text-sm">Welcome back to Loriya</p>
              </div>

              <div className="flex-grow overflow-y-auto scrollbar-hide">
                <form onSubmit={handleSignIn} className="space-y-4">
                  {/* Google Sign In */}
                  <Button
                    type="button"
                    onClick={handleGoogleAuth}
                    className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wide flex items-center justify-center gap-3"
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Continue with Google
                  </Button>

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-white/20" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-transparent px-2 text-white/60">Or</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="space-y-1">
                      <Label htmlFor="signin-email" className="text-white/90 text-sm">Email</Label>
                      <Input
                        id="signin-email"
                        type="email"
                        placeholder="Enter your email"
                        value={signInData.email}
                        onChange={(e) => setSignInData({ ...signInData, email: e.target.value })}
                        className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm h-10"
                      />
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor="signin-password" className="text-white/90 text-sm">Password</Label>
                      <div className="relative">
                        <Input
                          id="signin-password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your password"
                          value={signInData.password}
                          onChange={(e) => setSignInData({ ...signInData, password: e.target.value })}
                          className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm pr-10 h-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                        >
                          {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                    </div>

                    <div className="pt-2">
                      <Button
                        type="submit"
                        className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wide h-10"
                      >
                        Sign In
                      </Button>
                    </div>

                    <div className="text-center pt-2">
                      <button
                        type="button"
                        className="text-sm text-white/60 hover:text-white transition-colors"
                      >
                        Forgot your password?
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Sign Up View */}
          {currentView === "signup" && (
            <div className="h-full flex flex-col">
              <div className="flex-shrink-0 mb-6">
                <button
                  onClick={() => setCurrentView("main")}
                  className="flex items-center text-white/80 hover:text-white transition-colors mb-4"
                >
                  <ArrowLeft size={20} className="mr-2" />
                  Back
                </button>
                <h2 className="text-3xl font-light text-white mb-2">Create Account</h2>
                <p className="text-white/80 text-sm">Join the Loriya community</p>
              </div>

              <div className="flex-grow overflow-y-auto scrollbar-hide">
                <form onSubmit={handleSignUp} className="space-y-4">
                  {/* Google Sign Up */}
                  <Button
                    type="button"
                    onClick={handleGoogleAuth}
                    className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wide flex items-center justify-center gap-3"
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Continue with Google
                  </Button>

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-white/20" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-transparent px-2 text-white/60">Or</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="space-y-1">
                      <Label htmlFor="signup-name" className="text-white/90 text-sm">Full Name</Label>
                      <Input
                        id="signup-name"
                        type="text"
                        placeholder="Enter your full name"
                        value={signUpData.name}
                        onChange={(e) => setSignUpData({ ...signUpData, name: e.target.value })}
                        className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm h-10"
                      />
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor="signup-email" className="text-white/90 text-sm">Email</Label>
                      <Input
                        id="signup-email"
                        type="email"
                        placeholder="Enter your email"
                        value={signUpData.email}
                        onChange={(e) => setSignUpData({ ...signUpData, email: e.target.value })}
                        className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm h-10"
                      />
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor="signup-password" className="text-white/90 text-sm">Password</Label>
                      <div className="relative">
                        <Input
                          id="signup-password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Create a password"
                          value={signUpData.password}
                          onChange={(e) => setSignUpData({ ...signUpData, password: e.target.value })}
                          className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm pr-10 h-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                        >
                          {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor="signup-confirm-password" className="text-white/90 text-sm">Confirm Password</Label>
                      <div className="relative">
                        <Input
                          id="signup-confirm-password"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your password"
                          value={signUpData.confirmPassword}
                          onChange={(e) => setSignUpData({ ...signUpData, confirmPassword: e.target.value })}
                          className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm pr-10 h-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                        >
                          {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                    </div>

                    <div className="pt-2">
                      <Button
                        type="submit"
                        className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wide h-10"
                      >
                        Create Account
                      </Button>
                    </div>

                    <div className="text-center text-xs text-white/60 pt-2">
                      By continuing, you agree to our Terms of Service and Privacy Policy.
                    </div>
                  </div>
                </form>
              </div>
            </div>
          )}

        </div>
      </SheetContent>
    </Sheet>
  );
}
