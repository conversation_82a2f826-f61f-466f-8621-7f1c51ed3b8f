import { useState } from "react";
import { <PERSON>u, ChevronRight, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";

interface MenuItem {
  title: string;
  subcategories?: MenuItem[];
  layout?: string;
  href?: string;
}

const menuItems: MenuItem[] = [
  {
    title: "Gifts",
    subcategories: [
      { title: "Gifts for Her", href: "/gifts-for-her" },
      { title: "Gifts for Him", href: "/gifts-for-him" },
      { title: "Gifts for Home", href: "/gifts-for-home" },
      { title: "Gifts for Babies", href: "/gifts-for-babies" },
      { title: "Personalization", href: "/personalization" },
    ],
  },
  {
    title: "New",
    layout: "image-grid-variant",
    subcategories: [
      {
        title: "For Women",
        layout: "image-grid-variant",
        subcategories: [
          { title: "New Arrivals" },
          { title: "Pre-Fall 2025 Collection" },
          { title: "Spring 2025 Collection" },
          { title: "New Formal Collection" },
          { title: "Spring-Summer 2025 Show" },
        ],
      },
      {
        title: "For Men",
        layout: "image-grid-variant",
        subcategories: [
          { title: "New Arrivals" },
          { title: "Pre-Fall 2025 Collection" },
          { title: "Spring 2025 Collection" },
          { title: "New Formal Collection" },
          { title: "Spring-Summer 2025 Show" },
        ],
      },
    ],
  },
  {
    title: "Bags and Wallets",
    subcategories: [
      {
        title: "Women's Bags",
        subcategories: [
          { title: "Iconic Handbags" },
          { title: "All Handbags" },
          { title: "New In" },
          { title: "Monogram Signature" },
          { title: "Capucines" },
        ],
      },
      {
        title: "Men's Bags",
        subcategories: [
          { title: "Iconic Bags" },
          { title: "New In" },
          { title: "Monogram Signature" },
          { title: "All Bags" },
        ],
      },
      { title: "Women's Small Leather Goods" },
      { title: "Men's Small Leather Goods" },
      { title: "Personalization" },
    ],
  },
  {
    title: "Women",
    subcategories: [
      {
        title: "Handbags",
        layout: 'image-grid',
        subcategories: [
          { title: "Iconic Handbags" },
          { title: "All Handbags" },
          { title: "New In" },
          { title: "Monogram Signature" },
          { title: "Capucines" },
        ],
      },
      {
        title: "Wallets",
        layout: 'image-grid',
        subcategories: [
          { title: "All Small Leather Goods" },
          { title: "New In" },
          { title: "Wallets on Chain and Nano Bags" },
          { title: "Card and Key Holders" },
          { title: "Compact and Long Wallets" },
          { title: "LORIYA Essentials" },
        ],
      },
      {
        title: "Shoes",
        layout: 'image-grid',
        subcategories: [
          { title: "All Shoes" },
          { title: "Sneakers" },
          { title: "Boots & Booties" },
          { title: "Loafers and Ballerinas" },
          { title: "Mules and Slides" },
          { title: "Sandals and Espadrilles" },
          { title: "Pumps" },
        ],
      },
      { 
        title: "Accessories",
        layout: 'image-grid',
        subcategories: [
          { title: "Key Holders & Bag Charms" },
          { title: "Sunglasses" },
          { title: "Belts" },
          { title: "Silk Squares and Bandeaus" },
          { title: "Scarves" },
          { title: "Hats and Gloves" },
          { title: "Hair Accessories" },
          { title: "Home Accessories" },
          { title: "Tech Accessories" },
        ]
      },
      { 
        title: "Fashion Jewelry",
        layout: 'image-grid',
        subcategories: [
          { title: "All Fashion Jewelry" },
          { title: "Bracelets" },
          { title: "Rings" },
          { title: "Earrings" },
          { title: "Necklaces and Pendants" },
          { title: "Brooches" },
        ]
      },
      { 
        title: "Ready-to-Wear",
        layout: 'image-grid',
        subcategories: [
          { title: "All Ready-to-Wear" },
          { title: "Tops" },
          { title: "Skirts and Shorts" },
          { title: "Swimwear" },
          { title: "Dresses" },
          { title: "Denim" },
          { title: "Pants" },
          { title: "Knitwear" },
          { title: "Coats and Jackets" },
        ]
      },
    ],
  },
  {
    title: "Men",
    subcategories: [
      {
        title: "Bags",
        layout: 'image-grid',
        subcategories: [
          { title: "Iconic Bags" },
          { title: "New In" },
          { title: "Monogram Signature" },
          { title: "All Bags" },
        ],
      },
      {
        title: "Wallets",
        layout: 'image-grid',
        subcategories: [
          { title: "All Wallets" },
          { title: "New In" },
          { title: "Compact and Long Wallets" },
          { title: "Card Holders" },
          { title: "Pouches" },
          { title: "Mini Bags" },
        ]
      },
      { title: "Shoes",
        layout: 'image-grid',
        subcategories: [
          { title: "All Shoes" },
          { title: "Sneakers" },
          { title: "Loafers and Moccasins" },
          { title: "Buckles and Lace-Ups" },
          { title: "Boots" },
          { title: "Sandals" },
        ],
      },
      { title: "Accessories",
        layout: 'image-grid',
        subcategories: [
          { title: "Belts" },
          { title: "Sunglasses" },
          { title: "Key Holders, Key Chains and Bag Charms" },
          { title: "Hats, Beanies and Gloves" },
          { title: "Scarves" },
          { title: "Ties and Pocket Squares" },
          { title: "Silk and Bandanas" },
          { title: "Home Textile" },
          { title: "Tech Accessories" },
        ]
      },
      { 
        title: "Fashion Jewelry",
        layout: 'image-grid',
        subcategories: [
          { title: "All Fashion Jewelry" },
          { title: "Silver Collection" },
          { title: "Necklaces and Pendants" },
          { title: "Bracelets" },
          { title: "Rings" },
          { title: "Earrings" },
        ]
      },
      { 
        title: "Ready-to-Wear",
        layout: 'image-grid',
        subcategories: [
          { title: "All Ready-to-Wear" },
          { title: "T-Shirts and Polos" },
          { title: "Swimwear" },
          { title: "Pants" },
          { title: "Jeans" },
          { title: "Shirts" },
          { title: "Knitwear and Sweatshirts" },
          { title: "Coats and Outerwear" },
          { title: "Blazers and Jackets" },
        ]
      },
    ],
  },
  {
    title: "Jewelry",
    subcategories: [
      {
        title: "Collections",
        layout: 'image-grid',
        subcategories: [
          { title: "Color Blossom" },
          { title: "Idylle Blossom" },
          { title: "Le Damier de LORIYA" },
          { title: "Empreinte Collection" },
          { title: "Les Gastons LORIYA" },
          { title: "LORIYA Diamonds" },
          { title: "Silver Lockit" },
          { title: "Other Jewelry Collections" },
        ],
      },
      {
        title: "Categories",
        layout: 'image-grid',
        subcategories: [
          { title: "All Fine Jewelry" },
          { title: "Bracelets" },
          { title: "Earrings" },
          { title: "Necklaces and Pendants" },
          { title: "Rings" },
          { title: "Engagement Rings and Wedding Bands" },
          { title: "Men's Jewelry" },
        ],
      },
    ],
  },
  {
    title: "Watches",
    subcategories: [
      { title: "All Watches" },
      {
        title: "Watch Collections",
        layout: 'image-grid',
        subcategories: [
          { title: "Escale" },
          { title: "Tambour" },
          { title: "Tambour Convergence" },
          { title: "Tambour Taiko" },
          { title: "Original Tambour" },
          { title: "Objects of Time" },
        ],
      },
    ],
  },
  { 
    title: "Perfumes",
    subcategories: [
      { 
        title: "Iconic Scents",
        layout: 'image-grid',
        subcategories: [
          { title: "Imagination" },
          { title: "Attrape-Rêves" },
          { title: "Spell On You" },
          { title: "L'Immensité" },
          { title: "Ombre Nomade" },
        ]
      },
      { 
        title: "Categories",
        layout: 'image-grid',
        subcategories: [
          { title: "All Perfumes" },
          { title: "Women's Perfumes" },
          { title: "Men's Perfumes" },
          { title: "Cologne Perfumes" },
          { title: "Oud Perfumes" },
        ]
      },
    ]
  },
  { 
    title: "Travel and Home",
    subcategories: [
      {
        title: "Travel Bags and Rolling Luggage",
        layout: 'image-grid',
        subcategories: [
          { title: "Rolling Luggage" },
          { title: "Travel Bags" },
          { title: "Travel Accessories" },
          { title: "Personalization" },
        ],
      },
      {
        title: "Home and Art of Dining",
        layout: 'image-grid',
        subcategories: [
          { title: "Furniture" },
          { title: "Decoration" },
          { title: "Art of Dining" },
          { title: "Home Textiles" },
        ]
      },
    ]
  },
  { 
    title: "Services",
    subcategories: [
      { 
        title: "Personalization",
        layout: 'image-grid',
        subcategories: [
          { title: "Mon Monogram" },
          { title: "LORIYA & I Alma Personalization" },
          { title: "Bags and Small Leather Goods" },
          { title: "Travel" },
          { title: "Jewelry" },
          { title: "Perfumes" }
        ]
      },
      { title: "LORIYA Repairs" }
    ]
  },
];

export const MainMenu = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuStack, setMenuStack] = useState<{ items: MenuItem[]; title: string; layout?: string; }[]>(() => [{ items: menuItems, title: "Menu" }]);

  const handleSheetOpenChange = (open: boolean) => {
    setIsMenuOpen(open);
    if (!open) {
      setTimeout(() => setMenuStack([{ items: menuItems, title: "Menu" }]), 300);
    }
  };

  const activeMenu = menuStack[menuStack.length - 1];

  return (
    <Sheet open={isMenuOpen} onOpenChange={handleSheetOpenChange}>
      <SheetTrigger asChild>
        <button className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors">
          <Menu size={20} />
          <span className="text-sm font-light tracking-wide">MENU</span>
        </button>
      </SheetTrigger>
      <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full sm:max-w-sm text-white p-0">
        <div className="relative h-full overflow-x-hidden">
          {/* Main Menu */}
          <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "-translate-x-full" : "translate-x-0"}`}>
            <div className="flex flex-col h-full pt-24">
              <div className="flex-grow space-y-4 pb-8 overflow-y-auto px-8">
                {menuItems.map((item) => (
                  <div key={item.title}>
                    <a
                      href="#"
                      className="flex items-center justify-between w-full text-2xl font-light text-white hover:text-gray-300 transition-colors"
                      onClick={(e) => {
                        if (item.subcategories) {
                          e.preventDefault();
                          setMenuStack(stack => [...stack, {items: item.subcategories, title: item.title, layout: item.layout}]);
                        }
                      }}
                    >
                      <span>{item.title}</span>
                      {item.subcategories && (
                        <ChevronRight className="w-5 h-5" />
                      )}
                    </a>
                  </div>
                ))}
                <div>
                  <div className="text-2xl font-light text-white">The Maison LORIYA</div>
                </div>
              </div>
              <div className="flex-shrink-0 pt-4 px-8 pb-12">
                <Separator className="mb-4 bg-white/20" />
                <div className="space-y-1 text-sm font-light">
                  <p className="text-white/80">Can we help you?</p>
                  <p className="text-white tracking-wider">+1.866.LORIYA</p>
                  <div className="mt-4 space-y-2 text-sm font-light">
                    <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                      ADDRESS <span>→</span>
                    </a>
                    <a href="#" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider">
                      OPENING HOURS <span>→</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sub Menu Container */}
          <div className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${menuStack.length > 1 ? "translate-x-0" : "translate-x-full"}`}>
            {menuStack.length > 1 && (
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="pt-24 pb-6 px-8 flex-shrink-0">
                  <button
                    onClick={() => setMenuStack(stack => stack.slice(0, -1))}
                    className="flex items-center gap-2 text-white/80 hover:text-white transition-colors uppercase tracking-wider text-sm font-light"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span>Back</span>
                  </button>

                  <h2 className="text-3xl font-light text-white mt-8 mb-6">{activeMenu.title}</h2>
                </div>

                {/* Scrollable Content + Footer */}
                <div className="overflow-y-auto px-8 pb-12 flex-grow">
                  {/* Content */}
                  <div>
                    {activeMenu.layout === 'image-grid' ? (
                      <div>
                        <div className="mb-8">
                          <p className="text-sm font-light tracking-widest text-white/80 uppercase mb-4">New Arrivals</p>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <a href="#" className="group">
                                <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                  <p className="text-white/40 text-xs">Image</p>
                                </div>
                                <p className="text-xs font-light text-white/80 group-hover:text-white">Pre-Fall 2025 Collection</p>
                              </a>
                            </div>
                            <div>
                              <a href="#" className="group">
                                <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                  <p className="text-white/40 text-xs">Image</p>
                                </div>
                                <p className="text-xs font-light text-white/80 group-hover:text-white">Spring 2025 Collection</p>
                              </a>
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                          {activeMenu.items.map((subItem) => (
                            <a
                              key={subItem.title}
                              href="#"
                              className="group"
                              onClick={(e) => {
                                if (subItem.subcategories) {
                                  e.preventDefault();
                                  setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                }
                              }}
                            >
                              <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                                <p className="text-white/40 text-xs">Image</p>
                              </div>
                              <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                            </a>
                          ))}
                        </div>
                      </div>
                    ) : activeMenu.layout === 'image-grid-variant' ? (
                      <div className="grid grid-cols-2 gap-x-4 gap-y-8 text-center">
                        {activeMenu.items.map((subItem) => (
                          <a
                            key={subItem.title}
                            href="#"
                            className="group"
                            onClick={(e) => {
                              if (subItem.subcategories) {
                                e.preventDefault();
                                setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                              }
                            }}
                          >
                            <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-2 border border-white/20 flex items-center justify-center">
                              <p className="text-white/40 text-xs">Image</p>
                            </div>
                            <span className="text-sm font-light text-white/80 group-hover:text-white">{subItem.title}</span>
                          </a>
                        ))}
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {activeMenu.items.map((subItem) => (
                          subItem.href ? (
                            <Link
                              key={subItem.title}
                              to={subItem.href}
                              className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                            >
                              <span>{subItem.title}</span>
                            </Link>
                          ) : (
                            <a
                              key={subItem.title}
                              href="#"
                              className="flex items-center justify-between w-full text-xl font-light text-white/80 hover:text-white transition-colors"
                              onClick={(e) => {
                                if (subItem.subcategories) {
                                  e.preventDefault();
                                  setMenuStack(stack => [...stack, {items: subItem.subcategories, title: subItem.title, layout: subItem.layout}]);
                                }
                              }}
                            >
                              <span>{subItem.title}</span>
                              {subItem.subcategories && <ChevronRight className="w-5 h-5" />}
                            </a>
                          )
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="pt-4">
                    <Separator className="mb-4 bg-white/20" />
                    <div className="space-y-1 text-sm font-light">
                      <p className="text-white/80">Can we help you?</p>
                      <p className="text-white tracking-wider">+1.866.LORIYA</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
