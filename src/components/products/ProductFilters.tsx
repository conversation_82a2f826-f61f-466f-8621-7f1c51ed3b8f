import React, { useState } from 'react';
import { Filter, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ProductFilters as ProductFiltersType } from '@/types/product';
import { useProducts } from '@/hooks/useProducts';

interface ProductFiltersProps {
  filters: ProductFiltersType;
  onFiltersChange: (filters: Partial<ProductFiltersType>) => void;
  onClearFilters: () => void;
  className?: string;
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  className = ''
}) => {
  const { getAvailableCategories, getAvailableSubcategories, getPriceRange } = useProducts();
  const [isOpen, setIsOpen] = useState(false);
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    category: true,
    price: true,
    features: true
  });

  const categories = getAvailableCategories();
  const subcategories = getAvailableSubcategories(filters.category);
  const priceRange = getPriceRange();

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category: string) => {
    const newCategory = filters.category === category ? undefined : category;
    onFiltersChange({ 
      category: newCategory,
      subcategory: undefined // Reset subcategory when category changes
    });
  };

  const handleSubcategoryChange = (subcategory: string) => {
    const newSubcategory = filters.subcategory === subcategory ? undefined : subcategory;
    onFiltersChange({ subcategory: newSubcategory });
  };

  const handlePriceRangeChange = (values: number[]) => {
    onFiltersChange({
      priceRange: {
        min: values[0],
        max: values[1]
      }
    });
  };

  const handleFeatureToggle = (feature: keyof Pick<ProductFiltersType, 'inStock' | 'isNew' | 'isOnSale'>, checked: boolean) => {
    onFiltersChange({ [feature]: checked || undefined });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.subcategory) count++;
    if (filters.priceRange) count++;
    if (filters.inStock) count++;
    if (filters.isNew) count++;
    if (filters.isOnSale) count++;
    return count;
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Categories */}
      <Collapsible open={openSections.category} onOpenChange={() => toggleSection('category')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <h3 className="font-light text-sm tracking-wider uppercase text-white">Categories</h3>
          <ChevronDown className={`h-4 w-4 transition-transform text-white ${openSections.category ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-3">
          {categories.map((category) => (
            <div key={category} className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer">
                <Checkbox
                  checked={filters.category === category}
                  onCheckedChange={() => handleCategoryChange(category)}
                  className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
                />
                <span className="text-sm font-light text-white">{category}</span>
              </label>

              {/* Subcategories */}
              {filters.category === category && subcategories.length > 0 && (
                <div className="ml-6 space-y-2">
                  {subcategories.map((subcategory) => (
                    <label key={subcategory} className="flex items-center space-x-3 cursor-pointer">
                      <Checkbox
                        checked={filters.subcategory === subcategory}
                        onCheckedChange={() => handleSubcategoryChange(subcategory)}
                        className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
                      />
                      <span className="text-sm font-light text-white/80">{subcategory}</span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      <Separator className="bg-white/20" />

      {/* Price Range */}
      <Collapsible open={openSections.price} onOpenChange={() => toggleSection('price')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <h3 className="font-light text-sm tracking-wider uppercase text-white">Price Range</h3>
          <ChevronDown className={`h-4 w-4 transition-transform text-white ${openSections.price ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-4 pt-3">
          <div className="px-2">
            <Slider
              value={[
                filters.priceRange?.min || priceRange.min,
                filters.priceRange?.max || priceRange.max
              ]}
              onValueChange={handlePriceRangeChange}
              max={priceRange.max}
              min={priceRange.min}
              step={50}
              className="w-full"
            />
          </div>
          <div className="flex items-center justify-between text-sm font-light text-white/80">
            <span>${filters.priceRange?.min || priceRange.min}</span>
            <span>${filters.priceRange?.max || priceRange.max}</span>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator className="bg-white/20" />

      {/* Features */}
      <Collapsible open={openSections.features} onOpenChange={() => toggleSection('features')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <h3 className="font-light text-sm tracking-wider uppercase text-white">Features</h3>
          <ChevronDown className={`h-4 w-4 transition-transform text-white ${openSections.features ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-3">
          <label className="flex items-center space-x-3 cursor-pointer">
            <Checkbox
              checked={filters.inStock || false}
              onCheckedChange={(checked) => handleFeatureToggle('inStock', checked as boolean)}
              className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
            />
            <span className="text-sm font-light text-white">In Stock Only</span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <Checkbox
              checked={filters.isNew || false}
              onCheckedChange={(checked) => handleFeatureToggle('isNew', checked as boolean)}
              className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
            />
            <span className="text-sm font-light text-white">New Arrivals</span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <Checkbox
              checked={filters.isOnSale || false}
              onCheckedChange={(checked) => handleFeatureToggle('isOnSale', checked as boolean)}
              className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
            />
            <span className="text-sm font-light text-white">On Sale</span>
          </label>
        </CollapsibleContent>
      </Collapsible>

      {/* Clear Filters */}
      {getActiveFiltersCount() > 0 && (
        <>
          <Separator className="bg-white/20" />
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="w-full font-light tracking-wider border-white text-white hover:bg-white hover:text-black"
          >
            Clear All Filters
          </Button>
        </>
      )}
    </div>
  );

  return (
    <>
      {/* Mobile Filter Button */}
      <div className="lg:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="w-full justify-between font-light tracking-wider">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </div>
              {getActiveFiltersCount() > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80">
            <SheetHeader>
              <SheetTitle className="font-light tracking-wider">Filter Products</SheetTitle>
            </SheetHeader>
            <div className="mt-6">
              <FilterContent />
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Filters */}
      <div className={`hidden lg:block ${className}`}>
        <div className="sticky top-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="font-light text-lg tracking-wider text-white">Filters</h2>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="bg-white text-black">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </div>
          <FilterContent />
        </div>
      </div>
    </>
  );
};
