import React, { useState } from 'react';
import { Heart, ShoppingBag, Star, Minus, Plus, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Product, ProductVariant } from '@/types/product';
import { useCart } from '@/context/CartContext';
import { toast } from 'sonner';

interface ProductDetailProps {
  product: Product;
}

export const ProductDetail: React.FC<ProductDetailProps> = ({ product }) => {
  const { addItem } = useCart();
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedVariants, setSelectedVariants] = useState<{
    color?: ProductVariant;
    size?: ProductVariant;
    material?: ProductVariant;
  }>({});

  const handleAddToCart = () => {
    addItem(product, quantity, selectedVariants);
    toast.success(`${product.name} added to cart`);
  };

  const handleWishlist = () => {
    toast.success(`${product.name} added to wishlist`);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.shortDescription,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Product link copied to clipboard');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: product.currency
    }).format(price);
  };

  const getCurrentPrice = () => {
    const sizePrice = selectedVariants.size?.price;
    const colorPrice = selectedVariants.color?.price;
    return sizePrice || colorPrice || product.price;
  };

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  const handleVariantSelect = (type: 'color' | 'size' | 'material', variant: ProductVariant) => {
    setSelectedVariants(prev => ({
      ...prev,
      [type]: prev[type]?.id === variant.id ? undefined : variant
    }));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
      {/* Product Images */}
      <div className="space-y-4">
        {/* Main Image */}
        <div className="aspect-square bg-gray-50 rounded-lg overflow-hidden">
          <img
            src={product.images[selectedImage]?.url}
            alt={product.images[selectedImage]?.alt || product.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Thumbnail Images */}
        {product.images.length > 1 && (
          <div className="grid grid-cols-4 gap-2">
            {product.images.map((image, index) => (
              <button
                key={image.id}
                onClick={() => setSelectedImage(index)}
                className={`aspect-square bg-gray-50 rounded-lg overflow-hidden border-2 transition-colors ${
                  selectedImage === index ? 'border-black' : 'border-transparent hover:border-gray-300'
                }`}
              >
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Product Information */}
      <div className="space-y-6">
        {/* Header */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <p className="text-sm font-light tracking-widest text-gray-500 uppercase">
              {product.subcategory || product.category}
            </p>
            {product.isNew && (
              <Badge variant="secondary" className="bg-black text-white text-xs">
                NEW
              </Badge>
            )}
            {product.isOnSale && (
              <Badge variant="destructive" className="text-xs">
                SALE
              </Badge>
            )}
          </div>
          
          <h1 className="text-2xl lg:text-3xl font-light text-gray-900 mb-4">
            {product.name}
          </h1>

          {/* Rating */}
          {product.rating && (
            <div className="flex items-center gap-2 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(product.rating!)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                {product.rating} ({product.reviewCount} reviews)
              </span>
            </div>
          )}

          {/* Price */}
          <div className="flex items-center gap-3 mb-6">
            <span className="text-2xl font-light text-gray-900">
              {formatPrice(getCurrentPrice())}
            </span>
            {product.originalPrice && (
              <>
                <span className="text-lg text-gray-500 line-through font-light">
                  {formatPrice(product.originalPrice)}
                </span>
                {discountPercentage > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    -{discountPercentage}%
                  </Badge>
                )}
              </>
            )}
          </div>
        </div>

        {/* Short Description */}
        {product.shortDescription && (
          <p className="text-gray-600 font-light leading-relaxed">
            {product.shortDescription}
          </p>
        )}

        {/* Variants */}
        <div className="space-y-4">
          {/* Colors */}
          {product.variants?.colors && product.variants.colors.length > 0 && (
            <div>
              <h3 className="text-sm font-light tracking-wider uppercase mb-3">
                Color: {selectedVariants.color?.value || 'Select'}
              </h3>
              <div className="flex gap-2">
                {product.variants.colors.map((color) => (
                  <button
                    key={color.id}
                    onClick={() => handleVariantSelect('color', color)}
                    className={`w-12 h-12 rounded-full border-2 transition-colors ${
                      selectedVariants.color?.id === color.id
                        ? 'border-black'
                        : 'border-gray-200 hover:border-gray-400'
                    }`}
                    title={color.value}
                  >
                    <div className="w-full h-full rounded-full bg-gray-100" />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Sizes */}
          {product.variants?.sizes && product.variants.sizes.length > 0 && (
            <div>
              <h3 className="text-sm font-light tracking-wider uppercase mb-3">
                Size: {selectedVariants.size?.value || 'Select'}
              </h3>
              <div className="flex gap-2">
                {product.variants.sizes.map((size) => (
                  <button
                    key={size.id}
                    onClick={() => handleVariantSelect('size', size)}
                    className={`px-4 py-2 border text-sm font-light tracking-wider transition-colors ${
                      selectedVariants.size?.id === size.id
                        ? 'border-black bg-black text-white'
                        : 'border-gray-200 hover:border-gray-400'
                    }`}
                  >
                    {size.value}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Materials */}
          {product.variants?.materials && product.variants.materials.length > 0 && (
            <div>
              <h3 className="text-sm font-light tracking-wider uppercase mb-3">
                Material: {selectedVariants.material?.value || 'Select'}
              </h3>
              <div className="flex gap-2">
                {product.variants.materials.map((material) => (
                  <button
                    key={material.id}
                    onClick={() => handleVariantSelect('material', material)}
                    className={`px-4 py-2 border text-sm font-light tracking-wider transition-colors ${
                      selectedVariants.material?.id === material.id
                        ? 'border-black bg-black text-white'
                        : 'border-gray-200 hover:border-gray-400'
                    }`}
                  >
                    {material.value}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Quantity and Actions */}
        <div className="space-y-4">
          {/* Quantity */}
          <div>
            <h3 className="text-sm font-light tracking-wider uppercase mb-3">Quantity</h3>
            <div className="flex items-center gap-4">
              <div className="flex items-center border">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="h-10 w-10"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="px-4 py-2 min-w-[3rem] text-center">{quantity}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                  className="h-10 w-10"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <span className="text-sm text-gray-500">
                {product.stock} in stock
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleAddToCart}
              disabled={product.stock === 0}
              className="flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wider"
            >
              <ShoppingBag className="h-4 w-4 mr-2" />
              {product.stock === 0 ? 'OUT OF STOCK' : 'ADD TO CART'}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleWishlist}
              className="h-10 w-10"
            >
              <Heart className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleShare}
              className="h-10 w-10"
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Separator />

        {/* Product Details Tabs */}
        <Tabs defaultValue="description" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="description" className="font-light">Description</TabsTrigger>
            <TabsTrigger value="features" className="font-light">Features</TabsTrigger>
            <TabsTrigger value="specifications" className="font-light">Specifications</TabsTrigger>
          </TabsList>
          
          <TabsContent value="description" className="mt-6">
            <p className="text-gray-600 font-light leading-relaxed">
              {product.description}
            </p>
          </TabsContent>
          
          <TabsContent value="features" className="mt-6">
            {product.features && product.features.length > 0 ? (
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-600 font-light">
                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 font-light">No features listed for this product.</p>
            )}
          </TabsContent>
          
          <TabsContent value="specifications" className="mt-6">
            {product.specifications && Object.keys(product.specifications).length > 0 ? (
              <dl className="space-y-3">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                    <dt className="font-light text-gray-600">{key}</dt>
                    <dd className="font-light text-gray-900">{value}</dd>
                  </div>
                ))}
              </dl>
            ) : (
              <p className="text-gray-500 font-light">No specifications available for this product.</p>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
