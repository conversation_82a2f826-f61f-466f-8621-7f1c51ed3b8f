import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, ShoppingBag, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Product } from '@/types/product';
import { useCart } from '@/context/CartContext';
import { toast } from 'sonner';

interface ProductCardProps {
  product: Product;
  className?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, className = '' }) => {
  const { addItem } = useCart();
  const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addItem(product, 1);
    toast.success(`${product.name} added to cart`);
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toast.success(`${product.name} added to wishlist`);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: product.currency
    }).format(price);
  };

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  return (
    <Card className={`group relative overflow-hidden border-0 shadow-none hover:shadow-lg transition-all duration-300 ${className}`}>
      <Link to={`/products/${product.id}`} className="block">
        <div className="relative aspect-square overflow-hidden bg-gray-50">
          {/* Product Image */}
          <img
            src={primaryImage?.url}
            alt={primaryImage?.alt || product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            loading="lazy"
          />
          
          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {product.isNew && (
              <Badge variant="secondary" className="bg-black text-white text-xs font-light tracking-wider">
                NEW
              </Badge>
            )}
            {product.isOnSale && discountPercentage > 0 && (
              <Badge variant="destructive" className="bg-red-600 text-white text-xs font-light tracking-wider">
                -{discountPercentage}%
              </Badge>
            )}
            {product.stock === 0 && (
              <Badge variant="outline" className="bg-white/90 text-gray-600 text-xs font-light tracking-wider">
                SOLD OUT
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Button
              size="icon"
              variant="secondary"
              className="h-8 w-8 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleWishlist}
            >
              <Heart className="h-4 w-4" />
            </Button>
            {product.stock > 0 && (
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white shadow-sm"
                onClick={handleAddToCart}
              >
                <ShoppingBag className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Quick Add to Cart Overlay */}
          {product.stock > 0 && (
            <div className="absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                onClick={handleAddToCart}
                className="w-full bg-white text-black hover:bg-gray-100 font-light tracking-wider text-sm"
                size="sm"
              >
                ADD TO CART
              </Button>
            </div>
          )}
        </div>

        <CardContent className="p-4 space-y-2">
          {/* Product Category */}
          <p className="text-xs font-light tracking-widest text-gray-500 uppercase">
            {product.subcategory || product.category}
          </p>

          {/* Product Name */}
          <h3 className="font-light text-sm leading-tight text-gray-900 group-hover:text-gray-600 transition-colors line-clamp-2">
            {product.name}
          </h3>

          {/* Rating */}
          {product.rating && (
            <div className="flex items-center gap-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < Math.floor(product.rating!)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">
                ({product.reviewCount})
              </span>
            </div>
          )}

          {/* Price */}
          <div className="flex items-center gap-2">
            <span className="font-light text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-500 line-through font-light">
                {formatPrice(product.originalPrice)}
              </span>
            )}
          </div>

          {/* Color Variants Preview */}
          {product.variants?.colors && product.variants.colors.length > 0 && (
            <div className="flex items-center gap-1 pt-1">
              <span className="text-xs text-gray-500 mr-2">Colors:</span>
              <div className="flex gap-1">
                {product.variants.colors.slice(0, 3).map((color) => (
                  <div
                    key={color.id}
                    className="w-4 h-4 rounded-full border border-gray-200 bg-gray-100"
                    title={color.value}
                  />
                ))}
                {product.variants.colors.length > 3 && (
                  <span className="text-xs text-gray-500 ml-1">
                    +{product.variants.colors.length - 3}
                  </span>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  );
};
