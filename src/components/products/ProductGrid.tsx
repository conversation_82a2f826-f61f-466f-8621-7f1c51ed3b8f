import React from 'react';
import { Product } from '@/types/product';
import { ProductCard } from './ProductCard';

interface ProductGridProps {
  products: Product[];
  className?: string;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
}

export const ProductGrid: React.FC<ProductGridProps> = ({ 
  products, 
  className = '',
  columns = { mobile: 2, tablet: 3, desktop: 4 }
}) => {
  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-24 h-24 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center mb-4">
          <svg
            className="w-12 h-12 text-white/60"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"
            />
          </svg>
        </div>
        <h3 className="text-lg font-light text-white mb-2">No products found</h3>
        <p className="text-white/80 font-light max-w-md">
          We couldn't find any products matching your criteria. Try adjusting your filters or browse our featured collections.
        </p>
      </div>
    );
  }

  const getGridClasses = () => {
    const mobileClass = `grid-cols-${columns.mobile}`;
    const tabletClass = `md:grid-cols-${columns.tablet}`;
    const desktopClass = `lg:grid-cols-${columns.desktop}`;
    
    return `grid gap-6 ${mobileClass} ${tabletClass} ${desktopClass}`;
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          className="animate-in fade-in-50 duration-500"
        />
      ))}
    </div>
  );
};
